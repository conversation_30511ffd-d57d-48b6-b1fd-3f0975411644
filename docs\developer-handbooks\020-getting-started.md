# 02 - Getting Started

**Section:** 02-getting-started  
**Version:** 1.0  
**Last Updated:** July 2025  
**Prerequisites:** Basic Python knowledge, Git familiarity  
**Estimated Setup Time:** 15-30 minutes  

## Overview

This section provides complete guidance for setting up the Ultimate Electrical Designer development environment, from initial installation through your first successful test run. Follow these steps to get a fully functional development environment that meets the project's engineering-grade standards.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Quick Setup (5 Minutes)](#quick-setup-5-minutes)
- [Detailed Setup Guide](#detailed-setup-guide)
- [Environment Configuration](#environment-configuration)
- [Verification Steps](#verification-steps)
- [First Development Tasks](#first-development-tasks)
- [Troubleshooting](#troubleshooting)
- [Next Steps](#next-steps)

## Prerequisites

### System Requirements
- **Operating System:** Windows 10+, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **Python:** 3.13+ (3.12+ recommended for optimal performance)
- **Memory:** Minimum 8GB RAM (16GB recommended for large projects)
- **Storage:** 2GB free disk space for development environment
- **Git:** Latest version for version control

### Required Software

#### Python 3.13+
```bash
# Check Python version
python --version
# Should show Python 3.13.0 or higher

# If not installed, download from:
# https://www.python.org/downloads/
```

#### Git
```bash
# Check Git installation
git --version

# If not installed:
# Windows: Download from https://git-scm.com/
# macOS: Install via Homebrew: brew install git
# Linux: sudo apt-get install git
```

#### Optional but Recommended
- **VS Code:** With Python extension for optimal development experience
- **PostgreSQL:** For production-like database testing (SQLite used by default)
- **Docker:** For containerized development (optional)

## Quick Setup (5 Minutes)

For experienced developers who want to get started immediately:

```bash
# 1. Clone the repository
git clone https://github.com/debaneee/ultimate-electrical-designer.git
cd ultimate-electrical-designer

# 2. Navigate to backend
cd backend

# 3. Create and activate virtual environment
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux  
source venv/bin/activate

# 4. Complete development setup
make dev-setup

# 5. Start development server
make run-dev
```

**Verification:** Open http://localhost:8000/docs to see the API documentation.

## Detailed Setup Guide

### Step 1: Repository Setup

#### Clone the Repository
```bash
# Clone the main repository
git clone https://github.com/debaneee/ultimate-electrical-designer.git
cd ultimate-electrical-designer

# Verify repository structure
ls -la
# Should show: server/, client/, docs/, README.md, etc.
```

#### Navigate to Backend Directory
```bash
cd backend
pwd
# Should show: .../ultimate-electrical-designer/server
```

### Step 2: Python Environment Setup

#### Create Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Verify creation
ls venv/
# Should show: Scripts/ (Windows) or bin/ (macOS/Linux), lib/, etc.
```

#### Activate Virtual Environment
```bash
# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate

# Verify activation (prompt should show (venv))
which python
# Should show path to venv python
```

#### Upgrade Core Tools
```bash
# Upgrade pip and core tools
python -m pip install --upgrade pip setuptools wheel

# Verify pip version
pip --version
# Should show latest pip version
```

### Step 3: Project Dependencies

#### Install Development Dependencies
```bash
# Install all development dependencies
make install

# Alternative manual installation
pip install -e .[dev]

# Verify installation
pip list | grep fastapi
# Should show FastAPI and related packages
```

#### Verify Key Dependencies
```bash
# Check critical dependencies
python -c "import fastapi; print(f'FastAPI: {fastapi.__version__}')"
python -c "import sqlalchemy; print(f'SQLAlchemy: {sqlalchemy.__version__}')"
python -c "import pydantic; print(f'Pydantic: {pydantic.__version__}')"
```

### Step 4: Database Setup

#### Initialize Database
```bash
# Create necessary directories
make create-dirs

# Initialize database with migrations
make migrate-db

# Verify database creation
ls data/
# Should show: app_dev.db
```

#### Seed Development Data
```bash
# Load development data
make seed-dev

# Verify data loading
python -c "
from core.database.session import get_db
from core.models.user import User
db = next(get_db())
print(f'Users in database: {db.query(User).count()}')
"
```

### Step 5: Development Tools Setup

#### Install Pre-commit Hooks
```bash
# Install pre-commit hooks for code quality
make install-pre-commit

# Verify pre-commit installation
pre-commit --version
```

#### Configure IDE (VS Code)
```bash
# Create VS Code settings (optional)
mkdir -p .vscode
cat > .vscode/settings.json << EOF
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.formatting.provider": "ruff",
    "python.testing.pytestEnabled": true
}
EOF
```

## Environment Configuration

### Environment Variables

#### Create Environment File
```bash
# Copy example environment file
cp .env.example .env

# Edit environment variables
# Use your preferred editor to modify .env
```

#### Essential Environment Variables
```bash
# Database configuration
DATABASE_URL=sqlite:///./data/app_dev.db

# Security settings
SECRET_KEY=your-secret-key-here-generate-with-openssl-rand-hex-32
JWT_SECRET_KEY=your-jwt-secret-key-here

# Development settings
DEBUG=true
LOG_LEVEL=INFO
ENVIRONMENT=development

# API settings
API_V1_STR=/api/v1
PROJECT_NAME="Ultimate Electrical Designer"
```

#### Generate Secure Keys
```bash
# Generate SECRET_KEY
python -c "import secrets; print(f'SECRET_KEY={secrets.token_hex(32)}')"

# Generate JWT_SECRET_KEY  
python -c "import secrets; print(f'JWT_SECRET_KEY={secrets.token_hex(32)}')"

# Add these to your .env file
```

### Configuration Validation
```bash
# Validate configuration
python -c "
from config.settings import get_settings
settings = get_settings()
print(f'Environment: {settings.ENVIRONMENT}')
print(f'Database URL: {settings.DATABASE_URL}')
print('Configuration loaded successfully!')
"
```

## Verification Steps

### Step 1: Application Import Test
```bash
# Test application import
python -c "
from main import app
print('✅ Application imports successfully')
print(f'App title: {app.title}')
"
```

### Step 2: Database Connection Test
```bash
# Test database connection
python -c "
from core.database.session import engine
from sqlalchemy import text
with engine.connect() as conn:
    result = conn.execute(text('SELECT 1'))
    print('✅ Database connection successful')
"
```

### Step 3: Run Smoke Tests
```bash
# Run quick smoke tests
make test-smoke

# Expected output: All smoke tests should pass
# ✅ X tests passed in Y seconds
```

### Step 4: Start Development Server
```bash
# Start the development server
make run-dev

# Server should start on http://localhost:8000
# Check console output for any errors
```

### Step 5: API Documentation Access
```bash
# Open in browser or test with curl
curl http://localhost:8000/api/v1/health

# Expected response:
# {"status": "healthy", "timestamp": "..."}

# Access interactive docs at:
# http://localhost:8000/docs
```

## First Development Tasks

### 1. Explore the Codebase

#### Key Directories to Examine
```bash
# Core business logic
ls core/
# Should show: models/, services/, repositories/, schemas/, etc.

# API endpoints
ls api/v1/
# Should show: project_routes.py, component_routes.py, etc.

# Tests structure
ls tests/
# Should show: unit/, integration/, api/, etc.
```

#### Important Files to Understand
- `main.py` - Application entry point
- `core/models/` - Database models (SQLAlchemy)
- `core/schemas/` - Validation models (Pydantic)
- `core/services/` - Business logic
- `api/v1/` - API endpoints

### 2. Run Your First Test
```bash
# Run unit tests
make test-unit

# Run a specific test file
pytest tests/unit/test_project_service.py -v

# Run tests with coverage
make test-coverage
```

### 3. Make Your First Change

#### Simple Change Example
```python
# Edit api/v1/health_routes.py
# Add a new field to the health check response

@router.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",  # Add this line
        "environment": settings.ENVIRONMENT  # Add this line
    }
```

#### Test Your Change
```bash
# Restart the server
make run-dev

# Test the change
curl http://localhost:8000/api/v1/health

# Should now include version and environment fields
```

## Troubleshooting

### Common Setup Issues

#### Issue: Python Version Mismatch
**Symptoms:** `python --version` shows version < 3.13  
**Solution:**
```bash
# Install Python 3.13+ from python.org
# Or use pyenv for version management
pyenv install 3.13
pyenv local 3.13
```

#### Issue: Virtual Environment Activation Fails
**Symptoms:** `(venv)` doesn't appear in prompt  
**Solution:**
```bash
# Windows - try different activation method
venv\Scripts\activate.bat

# macOS/Linux - check shell type
echo $SHELL
# Use appropriate activation script
```

#### Issue: Database Migration Fails
**Symptoms:** `make migrate-db` shows errors  
**Solution:**
```bash
# Check database directory exists
mkdir -p data

# Reset database
rm -f data/app_dev.db
make migrate-db
```

#### Issue: Import Errors
**Symptoms:** `ModuleNotFoundError` when importing project modules  
**Solution:**
```bash
# Ensure you're in the backend directory
pwd
# Should end with /backend

# Reinstall in development mode
pip install -e .[dev]
```

#### Issue: Port Already in Use
**Symptoms:** `Address already in use` when starting server  
**Solution:**
```bash
# Find process using port 8000
lsof -i :8000  # macOS/Linux
netstat -ano | findstr :8000  # Windows

# Kill the process or use different port
make run-dev PORT=8001
```

### Getting Help

#### Check Logs
```bash
# Application logs
tail -f logs/ultimate_electrical_designer.log

# Test logs
ls test_logs/
```

#### Validate Environment
```bash
# Run comprehensive validation
make validate-environment

# Check system health
make health-check
```

## Next Steps

### Immediate Next Steps
1. **[Development Standards](030-development-standards.md)** - Learn about IEEE/IEC/EN compliance and coding standards
2. **[Unified Patterns](040-unified-patterns.md)** - Understand error handling and monitoring decorators
3. **[Backend Development](050-backend-development.md)** - Deep dive into the 5-layer architecture

### Development Workflow
```bash
# Daily development commands
make run-dev              # Start development server
make test                 # Run comprehensive tests  
make quality              # Check code quality
make unified-patterns     # Verify patterns compliance
```

### Learning Resources
- **API Documentation:** http://localhost:8000/docs (when server is running)
- **Architecture Specifications:** [Backend Architecture Docs](./backend/000-backend-specification.md)
- **Testing Guide:** [Testing Framework](070-testing-framework.md)

---

**Navigation:**  
← [Previous: Project Introduction](011-introduction.md) | [Handbook Home](001-cover.md) | [Next: Development Standards](030-development-standards.md) →

**Related Documentation:**
- [Design Principles](../002-robust-design-principles.md)
- [Testing Documentation](../../backend/scripts/testing.md)
