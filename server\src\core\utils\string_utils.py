# src/core/utils/string_utils.py
"""String Utilities.

This module provides common string manipulation functions for consistent
text processing across the application.

Key Features:
- URL-friendly slug generation
- Text sanitization and cleaning
- String hashing for quick comparisons
- Text truncation and padding utilities
"""

import hashlib
import re
import unicodedata

from config.logging_config import logger

# Regex patterns for text processing
SLUG_PATTERN = re.compile(r"[^\w\s-]", re.UNICODE)
WHITESPACE_PATTERN = re.compile(r"[-\s]+")
HTML_TAG_PATTERN = re.compile(r"<[^>]+>")
MULTIPLE_SPACES_PATTERN = re.compile(r"\s+")


def slugify(
    text: str,
    max_length: int | None = None,
    separator: str = "-",
    lowercase: bool = True,
) -> str:
    """Convert text to URL-friendly slug.

    Args:
        text: Text to slugify
        max_length: Maximum length of result (None for no limit)
        separator: Character to use as separator
        lowercase: Convert to lowercase

    Returns:
        str: URL-friendly slug

    Example:
        slugify("Hello World! 123") -> "hello-world-123"
        slugify("Café & Restaurant") -> "cafe-restaurant"

    """
    if not text:
        return ""

    # Normalize unicode characters
    text = unicodedata.normalize("NFKD", text)

    # Remove non-ASCII characters
    text = text.encode("ascii", "ignore").decode("ascii")

    # Convert to lowercase if requested
    if lowercase:
        text = text.lower()

    # Remove special characters except alphanumeric, spaces, and hyphens
    text = SLUG_PATTERN.sub("", text).strip()

    # Replace spaces and multiple hyphens with separator
    text = WHITESPACE_PATTERN.sub(separator, text)

    # Remove leading/trailing separators
    text = text.strip(separator)

    # Truncate if max_length specified
    if max_length and len(text) > max_length:
        text = text[:max_length].rstrip(separator)

    return text


def sanitize_text(
    text: str,
    remove_html: bool = True,
    normalize_whitespace: bool = True,
    max_length: int | None = None,
) -> str:
    """Sanitize text by removing unwanted characters and formatting.

    Args:
        text: Text to sanitize
        remove_html: Remove HTML tags
        normalize_whitespace: Normalize multiple spaces to single space
        max_length: Maximum length (None for no limit)

    Returns:
        str: Sanitized text

    """
    if not text:
        return ""

    result = text

    # Remove HTML tags
    if remove_html:
        result = HTML_TAG_PATTERN.sub("", result)

    # Normalize whitespace
    if normalize_whitespace:
        result = MULTIPLE_SPACES_PATTERN.sub(" ", result)

    # Strip leading/trailing whitespace
    result = result.strip()

    # Truncate if needed
    if max_length and len(result) > max_length:
        result = result[:max_length].rstrip()

    return result


def hash_string(text: str, algorithm: str = "md5", encoding: str = "utf-8") -> str:
    """Generate hash of string for quick comparisons (non-cryptographic).

    Args:
        text: Text to hash
        algorithm: Hash algorithm ('md5', 'sha1', 'sha256')
        encoding: Text encoding

    Returns:
        str: Hexadecimal hash string

    Raises:
        ValueError: If algorithm is not supported

    """
    if not text:
        return ""

    # Get hash function
    if algorithm == "md5":
        # Using MD5 for non-security string hashing only
        hasher = hashlib.md5(usedforsecurity=False)  # nosec B324
    elif algorithm == "sha1":
        # Using SHA1 for non-security string hashing only
        hasher = hashlib.sha1(usedforsecurity=False)  # nosec B324
    elif algorithm == "sha256":
        hasher = hashlib.sha256()
    else:
        raise ValueError(f"Unsupported hash algorithm: {algorithm}")

    # Hash the text
    hasher.update(text.encode(encoding))
    return hasher.hexdigest()


def truncate_string(
    text: str, max_length: int, suffix: str = "...", word_boundary: bool = True
) -> str:
    """Truncate string to maximum length with optional suffix.

    Args:
        text: Text to truncate
        max_length: Maximum length including suffix
        suffix: Suffix to add when truncating
        word_boundary: Try to break at word boundaries

    Returns:
        str: Truncated string

    """
    if not text or len(text) <= max_length:
        return text

    # Account for suffix length
    target_length = max_length - len(suffix)

    if target_length <= 0:
        return suffix[:max_length]

    truncated = text[:target_length]

    # Try to break at word boundary
    if word_boundary and " " in truncated:
        # Find last space
        last_space = truncated.rfind(" ")
        if last_space > target_length * 0.7:  # Don't break too early
            truncated = truncated[:last_space]

    return truncated + suffix


def pad_string(text: str, width: int, fill_char: str = " ", align: str = "left") -> str:
    """Pad string to specified width.

    Args:
        text: Text to pad
        width: Target width
        fill_char: Character to use for padding
        align: Alignment ('left', 'right', 'center')

    Returns:
        str: Padded string

    Raises:
        ValueError: If align is not supported

    """
    if len(text) >= width:
        return text

    if align == "left":
        return text.ljust(width, fill_char)
    if align == "right":
        return text.rjust(width, fill_char)
    if align == "center":
        return text.center(width, fill_char)
    raise ValueError(
        f"Unsupported alignment: {align}. Use 'left', 'right', or 'center'"
    )


def camel_to_snake(text: str) -> str:
    """Convert camelCase to snake_case.

    Args:
        text: CamelCase text

    Returns:
        str: snake_case text

    Example:
        camel_to_snake("camelCaseString") -> "camel_case_string"

    """
    # Insert underscore before uppercase letters
    result = re.sub("([a-z0-9])([A-Z])", r"\1_\2", text)
    return result.lower()


def snake_to_camel(text: str, capitalize_first: bool = False) -> str:
    """Convert snake_case to camelCase.

    Args:
        text: snake_case text
        capitalize_first: Capitalize first letter (PascalCase)

    Returns:
        str: camelCase text

    Example:
        snake_to_camel("snake_case_string") -> "snakeCaseString"
        snake_to_camel("snake_case_string", True) -> "SnakeCaseString"

    """
    components = text.split("_")
    if not components:
        return text

    # First component
    result = (
        components[0].lower() if not capitalize_first else components[0].capitalize()
    )

    # Remaining components (always capitalized)
    for component in components[1:]:
        result += component.capitalize()

    return result


def extract_numbers(text: str) -> list[str]:
    """Extract all numbers from text.

    Args:
        text: Text to extract numbers from

    Returns:
        list[str]: List of number strings found

    """
    return re.findall(r"\d+(?:\.\d+)?", text)


def remove_extra_whitespace(text: str) -> str:
    """Remove extra whitespace while preserving single spaces.

    Args:
        text: Text to clean

    Returns:
        str: Text with normalized whitespace

    """
    return MULTIPLE_SPACES_PATTERN.sub(" ", text).strip()


def is_email_like(text: str) -> bool:
    """Basic check if text looks like an email address.

    Args:
        text: Text to check

    Returns:
        bool: True if text looks like an email

    Note:
        This is a basic check, not full RFC validation

    """
    email_pattern = re.compile(r"^[^@]+@[^@]+\.[^@]+$")
    return bool(email_pattern.match(text.strip()))


def mask_sensitive_data(
    text: str, mask_char: str = "*", visible_chars: int = 4, mask_middle: bool = True
) -> str:
    """Mask sensitive data in string (e.g., credit cards, SSNs).

    Args:
        text: Text to mask
        mask_char: Character to use for masking
        visible_chars: Number of characters to keep visible
        mask_middle: Mask middle instead of end

    Returns:
        str: Masked string

    Example:
        mask_sensitive_data("1234567890123456") -> "1234********3456"

    """
    if len(text) <= visible_chars:
        return text

    if mask_middle:
        # Show first and last characters
        visible_each_side = visible_chars // 2
        start = text[:visible_each_side]
        end = text[-visible_each_side:] if visible_each_side > 0 else ""
        middle_length = len(text) - len(start) - len(end)
        return start + (mask_char * middle_length) + end
    # Show first characters only
    return text[:visible_chars] + (mask_char * (len(text) - visible_chars))
