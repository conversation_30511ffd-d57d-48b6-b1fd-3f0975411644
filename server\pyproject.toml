[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "ued-api-server"
version = "0.1.0"
description = "Backend server for Ultimate Electrical Designer"
authors = [
    "debaneee <<EMAIL>>"
]
license = "MIT"
readme = "README.md"
packages = [{ include = "src" }]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Manufacturing",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3.13",
    "Topic :: Scientific/Engineering",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Framework :: FastAPI",
]

# Main dependencies group
[tool.poetry.dependencies]
python = ">=3.13,<4.0"

# Core Framework
fastapi = ">=0.115.12"
uvicorn = { version = ">=0.34.3", extras = ["standard"] } # Correct way to specify extras
pydantic = ">=2.11.5"
pydantic-settings = ">=2.9.1"

# Database
sqlalchemy = ">=2.0.41"
alembic = ">=1.16.1"
psycopg2-binary = ">=2.9.9" # PostgreSQL/SQL Server support

# Authentication & Security
python-jose = { version = ">=3.3.0", extras = ["cryptography"] } # Correct way to specify extras
passlib = { version = ">=1.7.4", extras = ["bcrypt"] } # Correct way to specify extras
python-multipart = ">=0.0.6"
email-validator = ">=2.1.0"

# Utilities
python-dotenv = ">=1.0.0"
# --- This is how loguru should be specified now ---
loguru = { version = ">=0.7.3", python = ">=3.13,<4.0" }
# --- End loguru specification ---
structlog = ">=23.2.0"
rich = ">=13.7.0"
typer = ">=0.9.0"
uuid6 = ">=1.0.0"

# Scientific Computing
numpy = ">=1.24.0"
scipy = ">=1.11.0"
pandas = ">=2.1.0"

# File Processing
openpyxl = ">=3.1.0"
xlsxwriter = ">=3.1.0"
chardet = ">=5.2.0"

# Template Engine
jinja2 = ">=3.1.0"

# PDF Generation
weasyprint = ">=65.1"

# HTTP Client
httpx = ">=0.25.0"
requests = ">=2.31.0"

# JSON Schema Validation
jsonschema = ">=4.20.0"

# Development dependencies group
[tool.poetry.group.dev.dependencies]
# Testing
pytest = ">=7.4.0"
pytest-cov = ">=4.1.0"
pytest-asyncio = ">=0.21.0"
pytest-mock = ">=3.12.0"
pytest-xdist = ">=3.5.0"
pytest-benchmark = ">=4.0.0" # This is also in `performance`, but good for dev group too
pytest-timeout = ">=2.2.0"
pytest-html = ">=4.1.0"
coverage = { version = ">=7.3.0", extras = ["toml"] } # Correct extras syntax
factory-boy = ">=3.3.0"
faker = ">=20.1.0"
hypothesis = ">=6.135.26,<7.0.0"

# Code Quality
ruff = ">=0.1.6"
mypy = ">=1.7.0"
pre-commit = ">=3.6.0"

# Security Testing
bandit = ">=1.7.5"
safety = ">=2.3.0"

# Performance Testing
locust = ">=2.17.0"
memory-profiler = ">=0.61.0"

# Documentation
mkdocs = ">=1.5.0"
mkdocs-material = ">=9.4.0"
mkdocstrings = { version = ">=0.24.0", extras = ["python"] } # Correct extras syntax

# Test-specific dependencies
[tool.poetry.group.test.dependencies]
pytest = ">=7.4.0"
pytest-cov = ">=4.1.0"
pytest-asyncio = ">=0.21.0"
pytest-mock = ">=3.12.0"
factory-boy = ">=3.3.0"
faker = ">=20.1.0"

# Security-specific dependencies
[tool.poetry.group.security.dependencies]
bandit = ">=1.7.5"
safety = ">=2.3.0"

# Performance-specific dependencies
[tool.poetry.group.performance.dependencies]
locust = ">=2.17.0"
memory-profiler = ">=0.61.0"
pytest-benchmark = ">=4.0.0"

[tool.poetry.scripts]
ued-api-server = "main:app"

# ============================================================================
# TOOL CONFIGURATION
# ============================================================================

[tool.setuptools.packages.find]
where = ["."]
include = ["core*", "api*", "config*", "tests*"]
exclude = ["tests.*", "*.tests.*", "*.tests"]

[tool.setuptools.package-data]
"*" = ["*.json", "*.yaml", "*.yml", "*.toml", "*.ini"]

# ============================================================================
# PYTEST CONFIGURATION
# ============================================================================

[tool.pytest.ini_options]
minversion = "7.0"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--durations=10",
    "--timeout=600",  # Increased default timeout to 10 minutes for performance tests
    "--timeout-method=thread",  # Use thread-based timeout for better handling
]
asyncio_mode = "auto"
timeout = 600  # Default timeout increased to 10 minutes
timeout_func_only = true  # Only timeout individual test functions, not setup/teardown
markers = [
    # Test Categories
    "unit: Unit tests for individual components",
    "integration: Integration tests for component interactions",
    "service: Service layer tests",
    "api: API endpoint tests",
    "calculations: Calculation engine tests",
    "database: Database-related tests",
    "performance: Performance and load tests",
    "memory: Memory leak and usage tests",
    "standards: Standards compliance tests",
    "repository: Repository layer tests",
    "reports: Tests for report generation",
    "utils: Tests for utility functions",
    "schema: Schema validation tests",
    "import_export: Tests for import/export functionality",
    "security: Security and authentication tests",
    "middleware: Middleware component tests",
    "production_readiness: Production readiness validation tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::UserWarning:sqlalchemy.*",
    "ignore::UserWarning:pydantic.*",
    "ignore::pydantic.warnings.PydanticDeprecatedSince20",
    "ignore::pytest.PytestUnknownMarkWarning",
    "ignore::coverage.exceptions.CoverageWarning",
    "ignore::sqlalchemy.exc.SAWarning",
]
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

# ============================================================================
# COVERAGE CONFIGURATION
# ============================================================================

[tool.coverage.run]
source = ["core", "api", "config"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
    "*/.venv/*",
    "setup.py",
    "conftest.py",
]
branch = true
parallel = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
    "TYPE_CHECKING",
]
ignore_errors = true
show_missing = true
precision = 2
fail_under = 90

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"

# ============================================================================
# RUFF CONFIGURATION
# ============================================================================

[tool.ruff]
target-version = "py313"
line-length = 88
indent-width = 4
respect-gitignore = true

# Include and exclude patterns
include = ["*.py", "*.pyi", "**/pyproject.toml"]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",
    "htmlcov",
]

[tool.ruff.lint]
# Enable rules focused ONLY on performance and documentation
# Ignore simple linting errors: import order, unused imports, line length
select = [
    # Performance only
    "PERF", # Perflint (performance)

    # Documentation
    "D",    # pydocstyle (docstring conventions)

    # Critical functionality issues only
    "F821", # Undefined name (critical error)
    "F822", # Undefined name in __all__
    "F823", # Local variable referenced before assignment
]

# Ignore simple linting errors as requested - focus ONLY on performance and documentation
ignore = [
    # Docstring errors to ignore
    "D105",   # Missing docstring in magic method
    "D107",   # Missing docstring in __init__
    "D200",   # One-line docstring should fit on one line with quotes
    "D204",   # 1 blank line required after class docstring
    "D205",   # 1 blank line required between summary line and description
    "D209",   # Multi-line docstring closing quotes should be on a separate line
    "D212",   # Multi-line docstring should start at first line
    "D213",   # Multi-line docstring should start at second line
    "D400",   # Missing period
    "D401",   # First line should be in imperative mood
    "D402",   # First line should not be the function's or method's "signature"
    "D403",   # First word of the first line should be properly capitalized
    "D404",   # First line should not be the function's or method's "signature"
    "D405",   # First line should end with a period
    "D413",   # Missing blank line before a docstring's """ or '''
    "D415",   # Missing closing punctuation
    # Simple linting errors to ignore (as requested)
    "E",      # All pycodestyle errors (including line length, formatting)
    "W",      # All pycodestyle warnings
    "F401",   # Unused imports (MIGHT BREAK CODE!)
    "F402",   # Import module from line shadowed by loop variable
    "F403",   # 'from module import *' used; unable to detect undefined names
    "F404",   # Future import not at beginning of file
    "F405",   # Name may be undefined, or defined from star imports
    "F811",   # Redefined while unused
    "F841",   # Local variable assigned but never used
    "I",      # All import order issues (isort)
    "UP",     # All pyupgrade issues
    "C4",     # All comprehension issues
    "PIE",    # All unnecessary code issues
    "SIM",    # All code simplification issues
    "RET",    # All return statement issues
    "ARG",    # All unused arguments
    "PTH",    # All pathlib usage issues
    "T20",    # All print statement issues
    "PYI",    # All type stub issues
    "BLE",    # All bare except issues
    "TRY",    # All exception handling issues
    "ASYNC",  # All async/await issues
    "LOG",    # All logging issues
    "S",      # All security issues (bandit)
    "B",      # All bugbear issues
    "N",      # All naming convention issues

    # Keep only critical undefined name errors and syntax errors
    # These are handled by the select section above
]

# Allow fix for all enabled rules
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.per-file-ignores]
# Ignore ALL test files completely (as requested)
"tests/**/*.py" = ["ALL"]
"test_*.py" = ["ALL"]
"**/test_*.py" = ["ALL"]
"**/tests/**/*.py" = ["ALL"]
"conftest.py" = ["ALL"]
"**/conftest.py" = ["ALL"]

# Migration files (if using Alembic)
"**/alembic/**/*.py" = ["ALL"]

# Scripts and utilities (focus on core business logic only)
"scripts/**/*.py" = ["ALL"]
"**/scripts/**/*.py" = ["ALL"]
"reports/**/*.py" = ["ALL"]
"**/reports/**/*.py" = ["ALL"]
"docs/**/*.py" = ["ALL"]
"**/docs/**/*.py" = ["ALL"]
"data/**/*.py" = ["ALL"]
"**/data/**/*.py" = ["ALL"]

[tool.ruff.lint.isort]
known-first-party = ["core", "api", "config"]
known-third-party = ["fastapi", "sqlalchemy", "pydantic", "alembic"]
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]
split-on-trailing-comma = true

[tool.ruff.lint.flake8-bandit]
# Security checks configuration
check-typed-exception = true

[tool.ruff.lint.flake8-bugbear]
# Extend immutable calls to include FastAPI dependencies
extend-immutable-calls = ["fastapi.Depends", "fastapi.Query", "fastapi.Path"]

[tool.ruff.format]
# Formatting configuration
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

# ============================================================================
# MYPY CONFIGURATION
# ============================================================================

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_error_context = true
pretty = true

# Exclude problematic directories
exclude = [
    "scripts/",
    "venv/",
    "__pycache__/",
    ".venv/",
    "build/",
    "dist/",
    "*.egg-info/",
    ".history/",
    "htmlcov/",
    "reports/",
    "logs/"
]

# Fix module naming conflicts
explicit_package_bases = true
namespace_packages = true

# SQLAlchemy plugin
plugins = ["sqlalchemy.ext.mypy.plugin"]

# Per-module options
[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

[[tool.mypy.overrides]]
module = [
    "alembic.*",
    "uvicorn.*",
    "bcrypt.*",
    "passlib.*",
    "jose.*",
    "factory.*",
    "faker.*",
    "weasyprint.*",
    "openpyxl.*",
    "xlsxwriter.*",
    "chardet.*",
    "locust.*",
    "memory_profiler.*",
    "bandit.*",
    "safety.*",
    "numpy.*",
    "scipy.*",
    "pandas.*",
    "psutil.*"
]
ignore_missing_imports = true

# Specific overrides for electrical design modules
[[tool.mypy.overrides]]
module = "core.calculations.*"
# Allow some flexibility for complex electrical calculations
warn_return_any = false

# Override for migration scripts
[[tool.mypy.overrides]]
module = "alembic.versions.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

# Temporary overrides for modules with extensive type annotation needs
[[tool.mypy.overrides]]
module = [
    "core.calculations.*",
    "core.standards.*",
    "core.utils.*",
    "core.services.*",
    "core.schemas.*",
    "core.models.*"
]
# Relax some strict requirements during development
disallow_untyped_decorators = false
warn_return_any = false
no_implicit_optional = false

# Override for specific problematic modules
[[tool.mypy.overrides]]
module = [
    "core.models.electrical",
    "core.models.cable_selection_matrix",
    "core.calculations.electrical_sizing.*",
    "core.calculations.heat_loss.*"
]
# Skip type checking for modules with complex SQLAlchemy issues
ignore_errors = true
