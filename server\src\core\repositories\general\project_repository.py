# src/core/repositories/project_repository.py
"""Project Repository.

This module provides data access layer for Project entities, extending the base
repository with project-specific query methods and operations.
"""

from datetime import UTC

from sqlalchemy import and_, desc, select, update
from sqlalchemy.orm import Session, selectinload

# Unified systems imports
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.monitoring.unified_performance_monitor import monitor_repository_performance

from config.logging_config import logger
from src.core.models.general.project import Project
from src.core.repositories.base_repository import BaseRepository


class ProjectRepository(BaseRepository[Project]):
    """Repository for Project entity data access operations.

    Extends BaseRepository with project-specific query methods and
    enhanced error handling for project operations.
    """

    def __init__(self, db_session: Session):
        """Initialize the Project repository.

        Args:
            db_session: SQLAlchemy database session

        """
        super().__init__(db_session, Project)
        logger.debug("ProjectRepository initialized")

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    def get_active_projects(self, skip: int = 0, limit: int = 100) -> list[Project]:
        """Get list of active (non-deleted) projects.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Project]: List of active projects

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Retrieving active projects: skip={skip}, limit={limit}")

        stmt = (
            select(self.model)
            .where(self.model.is_deleted == False)
            .offset(skip)
            .limit(limit)
            .order_by(self.model.created_at.desc())
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} active projects")
        return results

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    def soft_delete_project(
        self, project_id: int, deleted_by_user_id: int | None = None
    ) -> bool:
        """Soft delete a project.

        Args:
            project_id: ID of project to delete
            deleted_by_user_id: ID of user performing the deletion

        Returns:
            bool: True if project was deleted, False if not found

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Soft deleting project {project_id}")

        from datetime import datetime

        update_data = {
            "is_deleted": True,
            "deleted_at": datetime.now(UTC),
            "deleted_by_user_id": deleted_by_user_id,
        }

        stmt = (
            update(self.model)
            .where(and_(self.model.id == project_id, self.model.is_deleted == False))
            .values(**update_data)
        )

        result = self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Project {project_id} soft deleted successfully")
            return True
        logger.debug(f"Project {project_id} not found or already deleted")
        return False

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    def get_projects_by_status(
        self, status: str, skip: int = 0, limit: int = 100
    ) -> list[Project]:
        """Get projects by status.

        Args:
            status: Project status to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Project]: List of projects with the specified status

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(
            f"Retrieving projects with status '{status}': skip={skip}, limit={limit}"
        )

        stmt = (
            select(self.model)
            .where(self.model.status == status)
            .order_by(desc(self.model.created_at))
            .offset(skip)
            .limit(limit)
        )
        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} projects with status '{status}'")
        return results

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    def update_project_status(self, project_id: int, status: str) -> Project:
        """Update project status.

        Args:
            project_id: ID of project to update
            status: New status value

        Returns:
            Project: Updated project

        Raises:
            ProjectNotFoundError: If project with given ID doesn't exist
            DatabaseError: If database operation fails

        """
        logger.debug(f"Updating project {project_id} status to '{status}'")

        project = self.get_by_id(project_id)

        project.status = status
        self.db_session.flush()

        logger.debug(f"Updated project {project_id} status to '{status}'")
        return project

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    def get_project_with_related_data(self, project_id: int) -> Project | None:
        """Get project with eagerly loaded related data to avoid N+1 queries.

        Args:
            project_id: ID of project to retrieve

        Returns:
            Optional[Project]: Project with related data or None

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Retrieving project {project_id} with related data")

        stmt = (
            select(self.model)
            .where(self.model.id == project_id)
            .options(
                selectinload(self.model.switchboards),
                selectinload(self.model.pipes),
                selectinload(self.model.tanks),
                selectinload(self.model.electrical_nodes),
                selectinload(self.model.cable_routes),
            )
        )
        result = self.db_session.scalar(stmt)

        if result:
            logger.debug(
                f"Retrieved project with related data: '{result.name}' (ID: {result.id})"
            )
        else:
            logger.debug(f"Project {project_id} not found")

        return result

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    def get_recent_projects(self, limit: int = 10) -> list[Project]:
        """Get recently created projects ordered by creation date.

        Args:
            limit: Maximum number of projects to return (default: 10)

        Returns:
            List[Project]: List of recent projects ordered by created_at descending

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug(f"Retrieving {limit} most recent projects")

        stmt = (
            select(self.model)
            .where(self.model.is_deleted == False)  # Exclude soft-deleted projects
            .order_by(self.model.created_at.desc())
            .limit(limit)
        )

        result = self.db_session.scalars(stmt).all()

        logger.debug(f"Retrieved {len(result)} recent projects")
        return result

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    def get_projects_by_client(
        self, client_name: str, skip: int = 0, limit: int = 100
    ) -> list[Project]:
        """Get projects by client name for professional project management workflows.

        This method enables client-based project filtering for professional electrical
        design workflows, supporting pagination and excluding soft-deleted projects.

        Args:
            client_name: Name of the client to filter projects by
            skip: Number of records to skip for pagination (default: 0)
            limit: Maximum number of records to return (default: 100)

        Returns:
            List[Project]: List of projects for the specified client, ordered by creation date

        Raises:
            DatabaseError: If database operation fails
            InvalidInputError: If client_name is empty or invalid

        """
        from src.core.errors.exceptions import InvalidInputError

        if not client_name or not client_name.strip():
            raise InvalidInputError("Client name cannot be empty")

        client_name = client_name.strip()
        logger.debug(
            f"Retrieving projects for client: '{client_name}', skip={skip}, limit={limit}"
        )

        stmt = (
            select(self.model)
            .where(
                and_(self.model.client == client_name, self.model.is_deleted == False)
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.created_at.desc())
        )

        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} projects for client '{client_name}'")
        return results

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    def count_active_projects(self) -> int:
        """Count total number of active projects.

        Returns:
            int: Number of active projects

        Raises:
            DatabaseError: If database operation fails

        """
        logger.debug("Counting active projects")

        from sqlalchemy import func

        stmt = select(func.count(self.model.id)).where(self.model.is_deleted == False)
        result = self.db_session.scalar(stmt)

        logger.debug(f"Total active projects: {result}")
        return result or 0

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    def get_projects_by_status(
        self, status: str, skip: int = 0, limit: int = 100
    ) -> list[Project]:
        """Get projects filtered by status for professional project management workflows.

        This method enables status-based project filtering for professional electrical
        design workflows, supporting pagination and excluding soft-deleted projects.

        Args:
            status: Project status to filter by (e.g., 'active', 'completed', 'on_hold')
            skip: Number of records to skip for pagination (default: 0)
            limit: Maximum number of records to return (default: 100)

        Returns:
            List[Project]: List of projects with the specified status, ordered by creation date

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving projects with status: {status}")

        stmt = (
            select(self.model)
            .where(and_(self.model.status == status, self.model.is_deleted == False))
            .offset(skip)
            .limit(limit)
            .order_by(self.model.created_at.desc())
        )

        results = list(self.db_session.scalars(stmt).all())

        logger.debug(f"Retrieved {len(results)} projects with status '{status}'")
        return results
