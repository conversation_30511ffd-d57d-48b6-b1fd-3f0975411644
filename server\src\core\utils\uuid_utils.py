# src/core/utils/uuid_utils.py
"""UUID Utilities.

This module provides utilities for UUID generation and management, with a focus
on UUIDv7 (timestamp-ordered UUIDs) for better database performance.

UUIDv7 Benefits:
- Lexicographically sortable (time-ordered)
- Database-friendly (reduces index fragmentation)
- Maintains global uniqueness
- Better write performance than UUIDv4
"""

import re
import sys
import uuid

from config.logging_config import logger

# Conditional import for uuid6 for Python versions < 3.12
_uuid6_available = False
if sys.version_info < (3, 12):
    try:
        import uuid6

        _uuid6_available = True
    except ImportError:
        logger.warning(
            "The 'uuid6' library is not installed. "
            "UUIDv7 generation will fall back to UUIDv4 for Python < 3.12. "
            "Consider installing it with 'pip install uuid6'."
        )

# UUID validation pattern
UUID_PATTERN = re.compile(
    r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", re.IGNORECASE
)


def generate_uuid7() -> uuid.UUID:
    """Generate a UUIDv7 (timestamp-ordered UUID).

    UUIDv7 incorporates a Unix timestamp in milliseconds as the most significant
    bits, making them naturally sortable and database-friendly.

    Returns:
        uuid.UUID: A new UUIDv7 instance

    Note:
        Python 3.12+ has native UUIDv7 support. For earlier versions,
        this falls back to UUIDv4 with a warning, or uses the 'uuid6' library
        if available.

    """
    try:
        # Python 3.12+ has native UUIDv7 support
        if sys.version_info >= (3, 12):
            # Directly try to use uuid.uuid7().
            return uuid.uuid7()  # type: ignore
        else:
            # Fallback for Python versions < 3.12
            if _uuid6_available:
                return uuid6.uuid7()
            else:
                # Warning already logged during import if uuid6 was not found
                logger.warning(
                    "UUIDv7 not available in Python < 3.12 and 'uuid6' library not found. "
                    "Falling back to UUIDv4."
                )
                return uuid.uuid4()
    except AttributeError:
        # Catch AttributeError specifically for uuid.uuid7() if it's truly missing
        logger.warning("uuid.uuid7() not found. Falling back to UUIDv4.")
        return uuid.uuid4()
    except Exception as e:
        logger.error(f"Error generating UUID: {e}")
        # Final fallback in case of any unexpected error
        return uuid.uuid4()


def generate_uuid7_str() -> str:
    """Generate a UUIDv7 as a string.

    Returns:
        str: A new UUIDv7 as a string representation

    """
    return str(generate_uuid7())


def is_valid_uuid(uuid_string: str) -> bool:
    """Validate if a string is a valid UUID format.

    Args:
        uuid_string: String to validate

    Returns:
        bool: True if valid UUID format, False otherwise

    """
    if not isinstance(uuid_string, str):
        return False

    return bool(UUID_PATTERN.match(uuid_string.strip()))


def uuid_to_str(uuid_obj: uuid.UUID) -> str:
    """Convert a UUID object to string.

    Args:
        uuid_obj: UUID object to convert

    Returns:
        str: String representation of the UUID

    Raises:
        ValueError: If uuid_obj is not a valid UUID

    """
    if not isinstance(uuid_obj, uuid.UUID):
        raise ValueError(f"Expected uuid.UUID, got {type(uuid_obj)}")

    return str(uuid_obj)


def str_to_uuid(uuid_string: str) -> uuid.UUID:
    """Convert a string to UUID object with validation.

    Args:
        uuid_string: String representation of UUID

    Returns:
        uuid.UUID: UUID object

    Raises:
        ValueError: If uuid_string is not a valid UUID format

    """
    if not is_valid_uuid(uuid_string):
        raise ValueError(f"Invalid UUID format: {uuid_string}")

    try:
        return uuid.UUID(uuid_string.strip())
    except ValueError as e:
        raise ValueError(f"Failed to parse UUID: {uuid_string}") from e


def normalize_uuid(uuid_input: str | uuid.UUID) -> uuid.UUID:
    """Normalize UUID input to UUID object.

    Args:
        uuid_input: Either a UUID string or UUID object

    Returns:
        uuid.UUID: Normalized UUID object

    Raises:
        ValueError: If input is not a valid UUID

    """
    if isinstance(uuid_input, uuid.UUID):
        return uuid_input
    if isinstance(uuid_input, str):
        return str_to_uuid(uuid_input)
    raise ValueError(f"Expected str or uuid.UUID, got {type(uuid_input)}")


def generate_short_uuid() -> str:
    """Generate a shortened UUID string (first 8 characters).

    Useful for display purposes or when a shorter identifier is needed.
    Note: This reduces uniqueness guarantees significantly.

    Returns:
        str: First 8 characters of a UUIDv7

    """
    return generate_uuid7_str()[:8]


# Database-specific utilities for SQLAlchemy integration


def uuid_column_default() -> str:
    """Default function for SQLAlchemy UUID columns stored as strings.

    Returns:
        str: New UUIDv7 as string

    Usage:
        Column(String(36), primary_key=True, default=uuid_column_default)

    """
    return generate_uuid7_str()


def uuid_column_default_callable():
    """Callable default for SQLAlchemy UUID columns.

    Returns:
        callable: Function that generates UUIDv7 strings

    Usage:
        Column(String(36), primary_key=True, default=uuid_column_default_callable())

    """
    return lambda: generate_uuid7_str()
