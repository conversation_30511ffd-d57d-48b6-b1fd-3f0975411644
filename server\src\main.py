# src/main.py
"""Main entrypoint for the application."""

import sys
import os
import typer
from loguru import logger
import uvicorn

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.app import app
from src.config.logging_config import setup_logging
from src.config.settings import Settings, settings

# Initialize Typer application
cli_app = typer.Typer(
    name="ultimate-electrical-designer",
    help="A CLI for managing the Ultimate Electrical Designer application backend.",
    pretty_exceptions_show_locals=False,  # Hide locals for cleaner exception messages
)

setup_logging()

DEBUG_MODE = os.getenv("DEBUG", "False").lower() == "true"
JSON_LOGS = os.getenv("JSON_LOGS", "False").lower() == "true"


# Now, use the logger globally
logger.info("Application starting up...")
logger.debug(f"Debug mode is {'ON' if DEBUG_MODE else 'OFF'}")
logger.debug(f"JSON logging is {'ON' if JSON_LOGS else 'OFF'}")


@cli_app.command()
def run(
    host: str = typer.Option("0.0.0.0", help="Host address to bind the server to."),
    port: int = typer.Option(settings.APP_PORT, help="Port to run the server on."),
    reload: bool = typer.Option(
        settings.DEBUG, help="Enable auto-reload for development."
    ),
):
    """Runs the FastAPI web server.
    Note: For direct Uvicorn invocation, use 'uvicorn src.app:app' from the server directory."""
    logger.info(f"Starting FastAPI server on http://{host}:{port}")
    logger.info(f"Application environment: {settings.ENVIRONMENT}")
    logger.info(f"Debug mode: {settings.DEBUG}")

    uvicorn.run(
        "app:app",
        host=host,
        port=port,
        reload=reload,
        log_level=settings.LOG_LEVEL.lower(),  # Use log_level from settings
    )


@cli_app.command()
def migrate():
    """Runs database migrations using Alembic."""
    logger.info("Starting database migrations...")
    original_dir = os.getcwd()
    try:
        # Ensure we're in the correct directory for alembic.ini
        if not os.path.exists("alembic.ini"):
            # If we're not in src directory, try to change to it
            if os.path.exists("src"):
                os.chdir("src")
                logger.debug(f"Changed current directory to: {os.getcwd()}")
            else:
                raise FileNotFoundError(
                    "Could not find alembic.ini or src directory"
                )
        else:
            logger.debug(f"Already in correct directory: {os.getcwd()}")

        from alembic import command
        from alembic.config import Config

        # Alembic will now find alembic.ini in the current directory
        alembic_cfg = Config("alembic.ini")
        # Use upgrade to apply migrations
        command.upgrade(alembic_cfg, "head")
        logger.info("Database migrations completed successfully.")

    except Exception as e:
        logger.error(f"Error during database migration: {e}", exc_info=True)
        typer.echo(f"Migration failed: {e}", err=True)
        raise typer.Exit(code=1)
    finally:
        # Change back to the original directory
        os.chdir(original_dir)
        logger.debug(f"Changed back to original directory: {os.getcwd()}")

    logger.info(
        "Database migration command finished (check logs for actual Alembic output)."
    )


@cli_app.command("create-superuser")
def create_superuser_command(
    username: str = typer.Argument(..., help="Username for the superuser."),
    password: str = typer.Argument(..., help="Password for the superuser."),
    email: str = typer.Argument(..., help="Email for the superuser."),
):
    """Creates a new superuser in the database."""
    logger.info(f"Attempting to create superuser: {username}")
    try:
        # Initialize database engine
        from src.core.database.initialization import initialize_database

        initialize_database()

        from src.core.database.session import (
            get_db_session,  # Use the standard session context manager
        )
        from src.core.services.general.user_service import UserService

        with get_db_session() as db_session:
            user_service = UserService(db_session)
            superuser = user_service.create_superuser(
                username=username, password=password, email=email
            )
        typer.echo(f"✅ Superuser '{superuser.name}' created successfully!")
        typer.echo(f"   - ID: {superuser.id}")
        typer.echo(f"   - Email: {superuser.email}")
        typer.echo(f"   - Active: {superuser.is_active}")
    except Exception as e:
        logger.error(f"Error creating superuser: {e}", exc_info=True)
        typer.echo(f"❌ Failed to create superuser: {e}", err=True)
        raise typer.Exit(code=1)


@cli_app.command("wipe-database")
def wipe_database_command(
    confirm: bool = typer.Option(
        False,
        "--confirm",
        help="Confirm database wipe operation (required for safety)",
    ),
    environment_check: bool = typer.Option(
        True,
        "--environment-check/--skip-environment-check",
        help="Verify environment is development before wiping (recommended)",
    ),
):
    """Completely wipe the database and migration history (DEVELOPMENT ONLY).

    This command performs a complete database reset including:
    - Removes all database files (SQLite) or drops all tables (PostgreSQL/SQL Server)
    - Clears Alembic migration history
    - Recreates database schema from current models

    ⚠️  WARNING: This operation is IRREVERSIBLE and will DELETE ALL DATA!

    Safety Features:
    - Requires explicit --confirm flag
    - Only allowed in development environment (unless --skip-environment-check)
    - Comprehensive logging of all operations
    - Validates environment settings before execution

    Example:
        python main.py wipe-database --confirm
    """
    logger.info("Database wipe operation requested...")

    # Import required modules
    try:
        from src.config.settings import Settings
        from src.core.database.engine import get_engine, close_engine
        from src.core.database.session import drop_tables, create_tables
        from alembic.config import Config
        from alembic import command
        import os
        import glob

        settings = Settings()

    except ImportError as e:
        logger.error(f"Failed to import required modules: {e}")
        typer.echo(f"❌ Import error: {e}", err=True)
        raise typer.Exit(code=1)

    # Safety check: Require explicit confirmation
    if not confirm:
        typer.echo("❌ Database wipe requires explicit confirmation.", err=True)
        typer.echo("   Use: python main.py wipe-database --confirm", err=True)
        typer.echo("   ⚠️  WARNING: This will DELETE ALL DATA!", err=True)
        raise typer.Exit(code=1)

    # Safety check: Environment verification
    if environment_check and settings.ENVIRONMENT != "development":
        typer.echo(
            f"❌ Database wipe is only allowed in development environment.", err=True
        )
        typer.echo(f"   Current environment: {settings.ENVIRONMENT}", err=True)
        typer.echo(
            f"   To override: use --skip-environment-check (NOT RECOMMENDED)", err=True
        )
        logger.error(f"Database wipe attempted in {settings.ENVIRONMENT} environment")
        raise typer.Exit(code=1)

    logger.warning(f"Starting database wipe in {settings.ENVIRONMENT} environment")
    typer.echo(f"🔥 Starting database wipe in {settings.ENVIRONMENT} environment...")
    typer.echo("   ⚠️  This operation will DELETE ALL DATA!")

    try:
        # Step 1: Close any existing database connections
        logger.info("Step 1: Closing database connections...")
        typer.echo("📝 Step 1: Closing database connections...")
        close_engine()

        # Step 2: Handle database file removal (SQLite) or table dropping (other DBs)
        logger.info("Step 2: Removing database files/tables...")
        typer.echo("📝 Step 2: Removing database files/tables...")

        if settings.effective_database_url.startswith("sqlite"):
            # For SQLite: Remove database files
            db_path = settings.SQLITE_DATABASE_PATH
            if os.path.exists(db_path):
                os.remove(db_path)
                logger.info(f"Removed SQLite database file: {db_path}")
                typer.echo(f"   ✅ Removed database file: {db_path}")

            # Remove WAL and SHM files if they exist
            for ext in ["-wal", "-shm"]:
                wal_file = db_path + ext
                if os.path.exists(wal_file):
                    os.remove(wal_file)
                    logger.info(f"Removed SQLite file: {wal_file}")
                    typer.echo(f"   ✅ Removed file: {wal_file}")
        else:
            # For other databases: Drop all tables
            logger.info("Dropping all database tables...")
            from src.core.database.engine import create_engine

            engine = create_engine()
            drop_tables(engine)
            close_engine()
            typer.echo("   ✅ Dropped all database tables")

        # Step 3: Clear Alembic migration history
        logger.info("Step 3: Clearing Alembic migration history...")
        typer.echo("📝 Step 3: Clearing Alembic migration history...")

        # Remove alembic_version table data by recreating database
        if settings.effective_database_url.startswith("sqlite"):
            # For SQLite, the file removal already cleared the history
            typer.echo("   ✅ Migration history cleared (SQLite file removed)")
        else:
            # For other databases, we need to drop the alembic_version table
            try:
                from src.core.database.engine import create_engine

                engine = create_engine()
                with engine.connect() as conn:
                    conn.execute("DROP TABLE IF EXISTS alembic_version")
                    conn.commit()
                close_engine()
                typer.echo("   ✅ Cleared alembic_version table")
            except Exception as e:
                logger.warning(f"Could not drop alembic_version table: {e}")
                typer.echo(f"   ⚠️  Could not drop alembic_version table: {e}")

        # Step 4: Recreate database schema from current models
        logger.info("Step 4: Recreating database schema...")
        typer.echo("📝 Step 4: Recreating database schema from current models...")

        from src.core.database.engine import create_engine

        engine = create_engine()
        create_tables(engine)
        close_engine()
        typer.echo("   ✅ Database schema recreated from current models")

        # Step 5: Initialize Alembic with current schema
        logger.info("Step 5: Initializing Alembic migration tracking...")
        typer.echo("📝 Step 5: Initializing Alembic migration tracking...")

        # Get the alembic.ini path (should be in src directory)
        alembic_cfg_path = "alembic.ini"
        if not os.path.exists(alembic_cfg_path):
            raise FileNotFoundError(f"Alembic config not found at: {alembic_cfg_path}")

        # Create Alembic config and stamp with head
        alembic_cfg = Config(alembic_cfg_path)
        command.stamp(alembic_cfg, "head")
        typer.echo("   ✅ Alembic migration tracking initialized")

        # Success summary
        logger.info("Database wipe completed successfully")
        typer.echo("\n🎉 Database wipe completed successfully!")
        typer.echo("   ✅ All data removed")
        typer.echo("   ✅ Migration history cleared")
        typer.echo("   ✅ Schema recreated from current models")
        typer.echo("   ✅ Alembic tracking initialized")
        typer.echo("\n📋 Next steps:")
        typer.echo("   1. Run: python main.py seed-data --environment development")
        typer.echo(
            "   2. Run: python main.py create-superuser 'Admin' 'Pass123' '<EMAIL>'"
        )
        typer.echo("   3. Run: python main.py run")

    except Exception as e:
        logger.error(f"Database wipe failed: {e}", exc_info=True)
        typer.echo(f"\n❌ Database wipe failed: {e}", err=True)
        typer.echo("   The database may be in an inconsistent state.")
        typer.echo("   Check logs for detailed error information.")
        raise typer.Exit(code=1)


if __name__ == "__main__":
    cli_app()

# Instantiate the settings object to make it globally available
settings = Settings()  # type: ignore
