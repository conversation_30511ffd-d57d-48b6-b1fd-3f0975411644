# 05 - Backend Development

**Section:** 05-backend-development
**Version:** 1.0
**Last Updated:** July 2025
**Prerequisites:** [Unified Patterns](040-unified-patterns.md) completed
**Estimated Reading Time:** 35 minutes

## Overview

The Ultimate Electrical Designer backend follows a strict 5-layer architecture pattern that ensures separation of concerns, maintainability, and scalability. This section provides comprehensive guidance for developing within this architecture, implementing CRUD patterns, service orchestration, and repository patterns that meet engineering-grade standards.

## Table of Contents

- [5-Layer Architecture Deep Dive](#5-layer-architecture-deep-dive)
- [API Layer Development](#api-layer-development)
- [Service Layer Patterns](#service-layer-patterns)
- [Repository Layer Implementation](#repository-layer-implementation)
- [Schema Layer Design](#schema-layer-design)
- [Model Layer Architecture](#model-layer-architecture)
- [CRUD Endpoint Factory](#crud-endpoint-factory)
- [Dependency Injection](#dependency-injection)
- [Cross-Layer Communication](#cross-layer-communication)
- [Performance Optimization](#performance-optimization)

## 5-Layer Architecture Deep Dive

### Architecture Overview

The 5-layer architecture ensures clean separation of concerns and maintainable code:

```
┌─────────────────────────────────────────────────────────────┐
│                        API Layer                            │
│  • FastAPI routes and HTTP handling                         │
│  • Request/response validation                              │
│  • Authentication and authorization                         │
│  • Thin controllers that delegate to services              │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Service Layer                          │
│  • Business logic implementation                            │
│  • Workflow orchestration                                   │
│  • Cross-cutting concerns (logging, monitoring)            │
│  • Transaction management                                   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Repository Layer                         │
│  • Data access abstraction                                  │
│  • Database query optimization                              │
│  • Error translation from database to application          │
│  • CRUD operations with business context                   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Schema Layer                           │
│  • Pydantic models for validation                          │
│  • Request/response serialization                          │
│  • Data transformation and mapping                         │
│  • API contract definitions                                │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                       Model Layer                           │
│  • SQLAlchemy ORM models                                   │
│  • Database schema definitions                             │
│  • Relationship mappings                                   │
│  • Data integrity enforcement                              │
└─────────────────────────────────────────────────────────────┘
```

### Layer Responsibilities

#### API Layer (`api/`)
**Primary Responsibility:** HTTP interface and request/response handling

**Key Functions:**
- Route definition and HTTP method handling
- Request validation using Pydantic schemas
- Response serialization and formatting
- Authentication and authorization enforcement
- Error handling and HTTP status code mapping

**Prohibited Activities:**
- Direct database access
- Business logic implementation
- Complex data transformations
- Direct model instantiation

#### Service Layer (`core/services/`)
**Primary Responsibility:** Business logic and workflow orchestration

**Key Functions:**
- Business rule implementation
- Workflow coordination between multiple repositories
- Transaction management
- Data validation and transformation
- Integration with external systems

**Design Principles:**
- Single responsibility per service
- Dependency injection for repositories
- Unified error handling patterns
- Performance monitoring integration

#### Repository Layer (`core/repositories/`)
**Primary Responsibility:** Data access abstraction and database operations

**Key Functions:**
- CRUD operations with business context
- Complex query implementation
- Database error translation
- Performance optimization
- Data integrity enforcement

**Design Patterns:**
- Generic base repository with common operations
- Entity-specific repositories with specialized methods
- Lazy loading for performance optimization
- Unified error handling for database operations

#### Schema Layer (`core/schemas/`)
**Primary Responsibility:** Data validation and serialization

**Key Functions:**
- Input validation for API requests
- Output serialization for API responses
- Data transformation between layers
- Business rule validation
- Type safety enforcement

**Implementation Standards:**
- Pydantic v2 with strict validation
- Custom validators for business rules
- Comprehensive field documentation
- Performance-optimized serialization

#### Model Layer (`core/models/`)
**Primary Responsibility:** Database schema and ORM definitions

**Key Functions:**
- SQLAlchemy ORM model definitions
- Database relationship mappings
- Constraint and index definitions
- Data integrity enforcement
- Migration support

**Design Requirements:**
- Complete type hints with Mapped annotations
- Proper relationship definitions with cascade settings
- Database constraints for data integrity
- Comprehensive docstrings for all models

#### Integrations Layer (`core/integrations/`)

**Primary Responsibility:** Handling all communication and data exchange with external systems and internal microservices.

**Key Functions:**
* **External API Client Implementation:** Developing and managing client code for interacting with third-party APIs (e.g., cloud services, data providers).
* **Internal Microservice Communication:** Implementing clients for communication with internal services written in other languages (e.g., C# CAD integration service, C# computation engine service).
* **Protocol Management:** Encapsulating the specifics of communication protocols (REST, gRPC, Message Queues).
* **Data Serialization/Deserialization:** Handling the conversion of data into formats suitable for external exchange (e.g., JSON, Protocol Buffers) and vice-versa.
* **Error Handling for External Calls:** Managing network errors, timeouts, and specific error responses from integrated systems.

**Design Principles:**
* **Loose Coupling:** Services in this layer should not directly depend on the internal implementation details of external systems.
* **Clear API Contracts:** Adhering to strict data schemas (e.g., using Pydantic for Python-side representation) defined for external interactions.
* **Resilience:** Implementing retry mechanisms, circuit breakers, and timeouts for robust external communication.
* **Observability:** Ensuring comprehensive logging, metrics, and tracing for all external interactions.
* **Pluggability:** Designed to easily swap out or add new integration clients without impacting core business logic.

## API Layer Development

### FastAPI Route Implementation

#### Standard Route Pattern
```python
from fastapi import APIRouter, Depends, HTTPException, status
from core.errors.unified_error_handler import handle_api_errors
from core.security.enhanced_dependencies import require_permissions, get_current_user
from core.services.project_service import ProjectService
from core.schemas.project_schemas import ProjectCreateSchema, ProjectResponse

router = APIRouter(prefix="/projects", tags=["projects"])

@router.post("", response_model=ProjectResponse, status_code=status.HTTP_201_CREATED)
@handle_api_errors("create_project")
@require_permissions("project:create")
async def create_project(
    project_data: ProjectCreateSchema,
    current_user: User = Depends(get_current_user),
    project_service: ProjectService = Depends(get_project_service)
) -> ProjectResponse:
    """Create a new electrical design project.

    Args:
        project_data: Project creation data with validation
        current_user: Authenticated user from JWT token
        project_service: Injected project service instance

    Returns:
        Created project with generated ID and timestamps

    Raises:
        HTTPException: 400 for validation errors, 409 for conflicts
    """
    project = project_service.create_project(
        project_data.model_dump(),
        created_by=current_user.id
    )
    return ProjectResponse.model_validate(project)
```

#### CRUD Endpoint Patterns
```python
# GET /projects - List with pagination and filtering
@router.get("", response_model=List[ProjectResponse])
@handle_api_errors("list_projects")
@require_permissions("project:read")
async def list_projects(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum records to return"),
    search: Optional[str] = Query(None, description="Search term for project name/description"),
    status: Optional[ProjectStatus] = Query(None, description="Filter by project status"),
    project_service: ProjectService = Depends(get_project_service)
) -> List[ProjectResponse]:
    """List projects with pagination and filtering."""
    projects = project_service.list_projects(
        skip=skip, limit=limit, search=search, status=status
    )
    return [ProjectResponse.model_validate(project) for project in projects]

# GET /projects/{project_id} - Get specific project
@router.get("/{project_id}", response_model=ProjectResponse)
@handle_api_errors("get_project")
@require_permissions("project:read")
async def get_project(
    project_id: int = Path(..., gt=0, description="Project ID"),
    project_service: ProjectService = Depends(get_project_service)
) -> ProjectResponse:
    """Get project by ID with complete details."""
    project = project_service.get_project_by_id(project_id)
    return ProjectResponse.model_validate(project)

# PUT /projects/{project_id} - Update project
@router.put("/{project_id}", response_model=ProjectResponse)
@handle_api_errors("update_project")
@require_permissions("project:update")
async def update_project(
    project_id: int = Path(..., gt=0, description="Project ID"),
    project_data: ProjectUpdateSchema = Body(...),
    current_user: User = Depends(get_current_user),
    project_service: ProjectService = Depends(get_project_service)
) -> ProjectResponse:
    """Update project with validation and audit trail."""
    project = project_service.update_project(
        project_id,
        project_data.model_dump(exclude_unset=True),
        updated_by=current_user.id
    )
    return ProjectResponse.model_validate(project)

# DELETE /projects/{project_id} - Soft delete project
@router.delete("/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
@handle_api_errors("delete_project")
@require_permissions("project:delete")
async def delete_project(
    project_id: int = Path(..., gt=0, description="Project ID"),
    current_user: User = Depends(get_current_user),
    project_service: ProjectService = Depends(get_project_service)
) -> None:
    """Soft delete project with audit trail."""
    project_service.delete_project(project_id, deleted_by=current_user.id)
```

### Request/Response Handling

#### Request Validation
```python
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from enum import Enum

class ProjectCreateSchema(BaseModel):
    """Schema for creating new electrical design projects."""

    name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Project name (must be unique)"
    )
    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Project description"
    )
    voltage_level: float = Field(
        ...,
        gt=0,
        le=1000000,
        description="System voltage level in volts"
    )
    environment_type: EnvironmentType = Field(
        ...,
        description="Environmental classification (indoor/outdoor/hazardous)"
    )
    ambient_temperature_min: float = Field(
        ...,
        ge=-50,
        le=100,
        description="Minimum ambient temperature in Celsius"
    )
    ambient_temperature_max: float = Field(
        ...,
        ge=-50,
        le=100,
        description="Maximum ambient temperature in Celsius"
    )

    @validator('ambient_temperature_max')
    def validate_temperature_range(cls, v, values):
        """Ensure maximum temperature is greater than minimum."""
        if 'ambient_temperature_min' in values and v <= values['ambient_temperature_min']:
            raise ValueError('Maximum temperature must be greater than minimum temperature')
        return v

    @validator('voltage_level')
    def validate_voltage_level(cls, v):
        """Validate voltage level against engineering standards."""
        # Standard voltage levels per IEC 60038
        standard_voltages = [
            120, 208, 240, 277, 480, 600,  # Low voltage
            2400, 4160, 6900, 13800,       # Medium voltage
            23000, 34500, 46000, 69000,    # High voltage
            115000, 138000, 230000, 345000, 500000, 765000  # Extra high voltage
        ]

        # Allow ±5% tolerance for standard voltages
        for standard_v in standard_voltages:
            if abs(v - standard_v) / standard_v <= 0.05:
                return v

        # Log non-standard voltage for review
        logger.warning(f"Non-standard voltage level specified: {v}V")
        return v

    class Config:
        """Pydantic configuration for optimal performance."""
        str_strip_whitespace = True
        validate_assignment = True
        use_enum_values = True
        schema_extra = {
            "example": {
                "name": "Industrial Heat Tracing Project",
                "description": "Heat tracing system for chemical processing plant",
                "voltage_level": 480,
                "environment_type": "hazardous",
                "ambient_temperature_min": -20,
                "ambient_temperature_max": 40
            }
        }
```

#### Response Serialization
```python
from datetime import datetime
from typing import Optional, List

class ProjectResponse(BaseModel):
    """Response schema for project data."""

    id: int = Field(..., description="Unique project identifier")
    name: str = Field(..., description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    voltage_level: float = Field(..., description="System voltage level in volts")
    environment_type: EnvironmentType = Field(..., description="Environmental classification")
    ambient_temperature_min: float = Field(..., description="Minimum ambient temperature")
    ambient_temperature_max: float = Field(..., description="Maximum ambient temperature")

    # Audit fields
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    created_by: int = Field(..., description="Creator user ID")
    updated_by: Optional[int] = Field(None, description="Last updater user ID")

    # Computed fields
    temperature_range: float = Field(..., description="Temperature range in Celsius")
    voltage_category: str = Field(..., description="Voltage classification")

    @validator('temperature_range', always=True)
    def compute_temperature_range(cls, v, values):
        """Compute temperature range from min/max values."""
        if 'ambient_temperature_min' in values and 'ambient_temperature_max' in values:
            return values['ambient_temperature_max'] - values['ambient_temperature_min']
        return 0

    @validator('voltage_category', always=True)
    def compute_voltage_category(cls, v, values):
        """Classify voltage level according to IEC standards."""
        if 'voltage_level' not in values:
            return "unknown"

        voltage = values['voltage_level']
        if voltage <= 1000:
            return "low_voltage"
        elif voltage <= 35000:
            return "medium_voltage"
        elif voltage <= 100000:
            return "high_voltage"
        else:
            return "extra_high_voltage"

    class Config:
        """Pydantic configuration for ORM integration."""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        schema_extra = {
            "example": {
                "id": 1,
                "name": "Industrial Heat Tracing Project",
                "description": "Heat tracing system for chemical processing plant",
                "voltage_level": 480,
                "environment_type": "hazardous",
                "ambient_temperature_min": -20,
                "ambient_temperature_max": 40,
                "created_at": "2025-07-05T10:00:00Z",
                "updated_at": "2025-07-05T10:00:00Z",
                "created_by": 1,
                "updated_by": None,
                "temperature_range": 60,
                "voltage_category": "low_voltage"
            }
        }
```

## Service Layer Patterns

### Service Class Architecture

#### Base Service Pattern
```python
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, List, Optional, Dict, Any
from core.errors.unified_error_handler import handle_service_errors
from core.monitoring.performance_decorators import monitor_service_performance
from core.repositories.base_repository import BaseRepository

T = TypeVar('T')

class BaseService(Generic[T], ABC):
    """Base service class with common patterns and unified error handling."""

    def __init__(self, repository: BaseRepository[T]):
        """Initialize service with repository dependency.

        Args:
            repository: Repository instance for data access
        """
        self.repository = repository
        self.logger = logger(self.__class__.__name__)

    @handle_service_errors("create_entity")
    @monitor_service_performance("create_entity")
    def create(self, entity_data: Dict[str, Any], **kwargs) -> T:
        """Create new entity with validation and audit trail.

        Args:
            entity_data: Entity creation data
            **kwargs: Additional context (created_by, etc.)

        Returns:
            Created entity instance

        Raises:
            ServiceError: If creation fails
            InvalidInputError: If validation fails
        """
        # Pre-creation validation
        validated_data = self._validate_create_data(entity_data)

        # Add audit information
        if 'created_by' in kwargs:
            validated_data['created_by'] = kwargs['created_by']

        # Create entity
        entity = self.repository.create(validated_data)

        # Post-creation processing
        self._post_create_processing(entity, **kwargs)

        self.logger.info(
            f"Entity created successfully",
            extra={
                "entity_id": entity.id,
                "entity_type": self.__class__.__name__,
                "created_by": kwargs.get('created_by')
            }
        )

        return entity

    @handle_service_errors("get_entity_by_id")
    def get_by_id(self, entity_id: int) -> T:
        """Get entity by ID with existence validation.

        Args:
            entity_id: Entity identifier

        Returns:
            Entity instance

        Raises:
            NotFoundError: If entity doesn't exist
        """
        entity = self.repository.get_by_id(entity_id)
        if not entity:
            raise NotFoundError(f"Entity with ID {entity_id} not found")
        return entity

    @handle_service_errors("update_entity")
    @monitor_service_performance("update_entity")
    def update(self, entity_id: int, update_data: Dict[str, Any], **kwargs) -> T:
        """Update entity with validation and audit trail.

        Args:
            entity_id: Entity identifier
            update_data: Update data
            **kwargs: Additional context (updated_by, etc.)

        Returns:
            Updated entity instance

        Raises:
            NotFoundError: If entity doesn't exist
            ServiceError: If update fails
        """
        # Verify entity exists
        existing_entity = self.get_by_id(entity_id)

        # Pre-update validation
        validated_data = self._validate_update_data(update_data, existing_entity)

        # Add audit information
        if 'updated_by' in kwargs:
            validated_data['updated_by'] = kwargs['updated_by']

        # Update entity
        updated_entity = self.repository.update(entity_id, validated_data)

        # Post-update processing
        self._post_update_processing(updated_entity, existing_entity, **kwargs)

        self.logger.info(
            f"Entity updated successfully",
            extra={
                "entity_id": entity_id,
                "entity_type": self.__class__.__name__,
                "updated_by": kwargs.get('updated_by'),
                "changes": list(validated_data.keys())
            }
        )

        return updated_entity

    @handle_service_errors("delete_entity")
    def delete(self, entity_id: int, **kwargs) -> None:
        """Soft delete entity with audit trail.

        Args:
            entity_id: Entity identifier
            **kwargs: Additional context (deleted_by, etc.)

        Raises:
            NotFoundError: If entity doesn't exist
            ServiceError: If deletion fails
        """
        # Verify entity exists
        entity = self.get_by_id(entity_id)

        # Pre-deletion validation
        self._validate_deletion(entity, **kwargs)

        # Soft delete entity
        self.repository.soft_delete(entity_id, deleted_by=kwargs.get('deleted_by'))

        # Post-deletion processing
        self._post_delete_processing(entity, **kwargs)

        self.logger.info(
            f"Entity deleted successfully",
            extra={
                "entity_id": entity_id,
                "entity_type": self.__class__.__name__,
                "deleted_by": kwargs.get('deleted_by')
            }
        )

    # Abstract methods for subclass implementation
    @abstractmethod
    def _validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data for entity creation."""
        pass

    @abstractmethod
    def _validate_update_data(self, data: Dict[str, Any], existing_entity: T) -> Dict[str, Any]:
        """Validate data for entity update."""
        pass

    def _validate_deletion(self, entity: T, **kwargs) -> None:
        """Validate entity can be deleted (override if needed)."""
        pass

    def _post_create_processing(self, entity: T, **kwargs) -> None:
        """Post-creation processing (override if needed)."""
        pass

    def _post_update_processing(self, updated_entity: T, original_entity: T, **kwargs) -> None:
        """Post-update processing (override if needed)."""
        pass

    def _post_delete_processing(self, entity: T, **kwargs) -> None:
        """Post-deletion processing (override if needed)."""
        pass
```

#### Concrete Service Implementation
```python
from typing import List, Optional, Dict, Any
from core.models.project import Project
from core.repositories.project_repository import ProjectRepository
from core.schemas.project_schemas import ProjectCreateSchema, ProjectUpdateSchema
from core.services.base_service import BaseService

class ProjectService(BaseService[Project]):
    """Service for electrical design project management."""

    def __init__(self, project_repository: ProjectRepository):
        """Initialize project service.

        Args:
            project_repository: Project repository instance
        """
        super().__init__(project_repository)
        self.project_repository = project_repository

    @handle_service_errors("list_projects")
    @monitor_service_performance("list_projects")
    def list_projects(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        status: Optional[str] = None,
        created_by: Optional[int] = None
    ) -> List[Project]:
        """List projects with filtering and pagination.

        Args:
            skip: Number of records to skip
            limit: Maximum records to return
            search: Search term for name/description
            status: Filter by project status
            created_by: Filter by creator user ID

        Returns:
            List of projects matching criteria
        """
        return self.project_repository.list_with_filters(
            skip=skip,
            limit=limit,
            search=search,
            status=status,
            created_by=created_by
        )

    @handle_service_errors("get_project_statistics")
    @monitor_service_performance("get_project_statistics")
    def get_project_statistics(self, project_id: int) -> Dict[str, Any]:
        """Get comprehensive project statistics.

        Args:
            project_id: Project identifier

        Returns:
            Dictionary containing project statistics
        """
        project = self.get_by_id(project_id)

        # Calculate project statistics
        stats = {
            "project_id": project_id,
            "total_pipes": len(project.pipes),
            "total_tanks": len(project.tanks),
            "total_heat_circuits": sum(1 for pipe in project.pipes if pipe.ht_circuit),
            "voltage_level": project.voltage_level,
            "temperature_range": project.ambient_temperature_max - project.ambient_temperature_min,
            "environment_type": project.environment_type,
            "created_at": project.created_at,
            "last_modified": project.updated_at
        }

        # Add calculated engineering metrics
        if project.pipes:
            total_pipe_length = sum(pipe.length for pipe in project.pipes)
            stats["total_pipe_length"] = total_pipe_length
            stats["average_pipe_diameter"] = sum(pipe.diameter for pipe in project.pipes) / len(project.pipes)

        return stats

    def _validate_create_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate project creation data.

        Args:
            data: Raw project data

        Returns:
            Validated project data

        Raises:
            InvalidInputError: If validation fails
        """
        # Use Pydantic schema for validation
        schema = ProjectCreateSchema(**data)
        validated_data = schema.model_dump()

        # Additional business rule validation
        self._validate_project_name_uniqueness(validated_data['name'])
        self._validate_voltage_level(validated_data['voltage_level'])
        self._validate_temperature_range(
            validated_data['ambient_temperature_min'],
            validated_data['ambient_temperature_max']
        )

        return validated_data

    def _validate_update_data(self, data: Dict[str, Any], existing_project: Project) -> Dict[str, Any]:
        """Validate project update data.

        Args:
            data: Update data
            existing_project: Current project state

        Returns:
            Validated update data
        """
        # Use Pydantic schema for validation
        schema = ProjectUpdateSchema(**data)
        validated_data = schema.model_dump(exclude_unset=True)

        # Check name uniqueness if name is being changed
        if 'name' in validated_data and validated_data['name'] != existing_project.name:
            self._validate_project_name_uniqueness(validated_data['name'])

        # Validate voltage level if being changed
        if 'voltage_level' in validated_data:
            self._validate_voltage_level(validated_data['voltage_level'])

        # Validate temperature range if being changed
        if 'ambient_temperature_min' in validated_data or 'ambient_temperature_max' in validated_data:
            min_temp = validated_data.get('ambient_temperature_min', existing_project.ambient_temperature_min)
            max_temp = validated_data.get('ambient_temperature_max', existing_project.ambient_temperature_max)
            self._validate_temperature_range(min_temp, max_temp)

        return validated_data

    def _validate_deletion(self, project: Project, **kwargs) -> None:
        """Validate project can be deleted.

        Args:
            project: Project to delete
            **kwargs: Additional context

        Raises:
            BusinessRuleError: If project cannot be deleted
        """
        # Check if project has active heat tracing circuits
        active_circuits = [pipe for pipe in project.pipes if pipe.ht_circuit and pipe.ht_circuit.is_active]
        if active_circuits:
            raise BusinessRuleError(
                f"Cannot delete project with {len(active_circuits)} active heat tracing circuits"
            )

        # Check if project has pending calculations
        if hasattr(project, 'calculation_jobs') and project.calculation_jobs:
            pending_jobs = [job for job in project.calculation_jobs if job.status == 'pending']
            if pending_jobs:
                raise BusinessRuleError(
                    f"Cannot delete project with {len(pending_jobs)} pending calculation jobs"
                )

    def _validate_project_name_uniqueness(self, name: str) -> None:
        """Validate project name is unique.

        Args:
            name: Project name to validate

        Raises:
            DuplicateError: If name already exists
        """
        existing_project = self.project_repository.get_by_name(name)
        if existing_project:
            raise DuplicateError(f"Project with name '{name}' already exists")

    def _validate_voltage_level(self, voltage_level: float) -> None:
        """Validate voltage level against engineering standards.

        Args:
            voltage_level: Voltage level in volts

        Raises:
            InvalidInputError: If voltage level is invalid
        """
        if voltage_level <= 0:
            raise InvalidInputError("Voltage level must be positive")

        if voltage_level > 1000000:  # 1 MV limit
            raise InvalidInputError("Voltage level exceeds maximum allowed (1 MV)")

        # Warn for non-standard voltage levels
        standard_voltages = [120, 208, 240, 277, 480, 600, 2400, 4160, 6900, 13800]
        if not any(abs(voltage_level - std_v) / std_v <= 0.05 for std_v in standard_voltages):
            self.logger.warning(
                f"Non-standard voltage level specified: {voltage_level}V",
                extra={"voltage_level": voltage_level}
            )

    def _validate_temperature_range(self, min_temp: float, max_temp: float) -> None:
        """Validate temperature range is reasonable.

        Args:
            min_temp: Minimum temperature in Celsius
            max_temp: Maximum temperature in Celsius

        Raises:
            InvalidInputError: If temperature range is invalid
        """
        if min_temp >= max_temp:
            raise InvalidInputError("Maximum temperature must be greater than minimum temperature")

        if max_temp - min_temp > 150:
            raise InvalidInputError("Temperature range exceeds reasonable limits (150°C)")

        if min_temp < -50 or max_temp > 100:
            self.logger.warning(
                f"Extreme temperature range specified: {min_temp}°C to {max_temp}°C",
                extra={"min_temp": min_temp, "max_temp": max_temp}
            )

    def _post_create_processing(self, project: Project, **kwargs) -> None:
        """Post-creation processing for projects.

        Args:
            project: Created project
            **kwargs: Additional context
        """
        # Initialize default project settings
        self._initialize_project_defaults(project)

        # Create audit log entry
        self._create_audit_log_entry(
            project_id=project.id,
            action="project_created",
            user_id=kwargs.get('created_by'),
            details={"project_name": project.name}
        )

    def _initialize_project_defaults(self, project: Project) -> None:
        """Initialize default settings for new project.

        Args:
            project: Project to initialize
        """
        # Set default calculation parameters based on environment type
        if project.environment_type == "hazardous":
            # Set ATEX-compliant defaults
            project.default_cable_type = "armored"
            project.default_protection_class = "Ex d"
        elif project.environment_type == "outdoor":
            # Set weather-resistant defaults
            project.default_cable_type = "outdoor_rated"
            project.default_protection_class = "IP65"
        else:
            # Indoor defaults
            project.default_cable_type = "standard"
            project.default_protection_class = "IP20"

        # Commit default settings
        self.repository.update(project.id, {
            "default_cable_type": project.default_cable_type,
            "default_protection_class": project.default_protection_class
        })
```

## Repository Layer Implementation

### Base Repository Pattern

#### Generic Base Repository
```python
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, List, Optional, Dict, Any, Type
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy import and_, or_, func, desc, asc
from core.errors.unified_error_handler import handle_repository_errors
from core.database.session import get_db
from core.models.base import BaseModel

T = TypeVar('T', bound=BaseModel)

class BaseRepository(Generic[T], ABC):
    """Base repository with common CRUD operations and unified error handling."""

    def __init__(self, db: Session, model_class: Type[T]):
        """Initialize repository with database session and model class.

        Args:
            db: SQLAlchemy database session
            model_class: SQLAlchemy model class
        """
        self.db = db
        self.model_class = model_class
        self.logger = logger(self.__class__.__name__)

    @handle_repository_errors("create")
    def create(self, entity_data: Dict[str, Any]) -> T:
        """Create new entity in database.

        Args:
            entity_data: Entity creation data

        Returns:
            Created entity instance

        Raises:
            RepositoryError: If creation fails
            DuplicateError: If unique constraint violated
        """
        try:
            entity = self.model_class(**entity_data)
            self.db.add(entity)
            self.db.commit()
            self.db.refresh(entity)

            self.logger.debug(
                f"Entity created successfully",
                extra={
                    "entity_type": self.model_class.__name__,
                    "entity_id": entity.id
                }
            )

            return entity

        except IntegrityError as e:
            self.db.rollback()
            if "unique constraint" in str(e).lower():
                raise DuplicateError(f"Entity with these values already exists")
            raise RepositoryError(f"Database integrity error: {str(e)}")
        except SQLAlchemyError as e:
            self.db.rollback()
            raise RepositoryError(f"Database error during creation: {str(e)}")

    @handle_repository_errors("get_by_id")
    def get_by_id(self, entity_id: int) -> Optional[T]:
        """Get entity by ID.

        Args:
            entity_id: Entity identifier

        Returns:
            Entity instance or None if not found
        """
        try:
            entity = self.db.query(self.model_class).filter(
                self.model_class.id == entity_id,
                self.model_class.deleted_at.is_(None)  # Exclude soft-deleted
            ).first()

            return entity

        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error during retrieval: {str(e)}")

    @handle_repository_errors("get_all")
    def get_all(
        self,
        skip: int = 0,
        limit: int = 100,
        order_by: Optional[str] = None,
        order_direction: str = "asc"
    ) -> List[T]:
        """Get all entities with pagination and ordering.

        Args:
            skip: Number of records to skip
            limit: Maximum records to return
            order_by: Field name to order by
            order_direction: Order direction ('asc' or 'desc')

        Returns:
            List of entities
        """
        try:
            query = self.db.query(self.model_class).filter(
                self.model_class.deleted_at.is_(None)
            )

            # Apply ordering
            if order_by and hasattr(self.model_class, order_by):
                order_field = getattr(self.model_class, order_by)
                if order_direction.lower() == "desc":
                    query = query.order_by(desc(order_field))
                else:
                    query = query.order_by(asc(order_field))
            else:
                # Default ordering by ID
                query = query.order_by(asc(self.model_class.id))

            # Apply pagination
            entities = query.offset(skip).limit(limit).all()

            return entities

        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error during retrieval: {str(e)}")

    @handle_repository_errors("update")
    def update(self, entity_id: int, update_data: Dict[str, Any]) -> T:
        """Update entity by ID.

        Args:
            entity_id: Entity identifier
            update_data: Update data

        Returns:
            Updated entity instance

        Raises:
            NotFoundError: If entity doesn't exist
            RepositoryError: If update fails
        """
        try:
            entity = self.get_by_id(entity_id)
            if not entity:
                raise NotFoundError(f"Entity with ID {entity_id} not found")

            # Update entity attributes
            for key, value in update_data.items():
                if hasattr(entity, key):
                    setattr(entity, key, value)

            # Set updated timestamp
            if hasattr(entity, 'updated_at'):
                entity.updated_at = func.now()

            self.db.commit()
            self.db.refresh(entity)

            self.logger.debug(
                f"Entity updated successfully",
                extra={
                    "entity_type": self.model_class.__name__,
                    "entity_id": entity_id,
                    "updated_fields": list(update_data.keys())
                }
            )

            return entity

        except SQLAlchemyError as e:
            self.db.rollback()
            raise RepositoryError(f"Database error during update: {str(e)}")

    @handle_repository_errors("delete")
    def delete(self, entity_id: int) -> None:
        """Hard delete entity by ID.

        Args:
            entity_id: Entity identifier

        Raises:
            NotFoundError: If entity doesn't exist
            RepositoryError: If deletion fails
        """
        try:
            entity = self.get_by_id(entity_id)
            if not entity:
                raise NotFoundError(f"Entity with ID {entity_id} not found")

            self.db.delete(entity)
            self.db.commit()

            self.logger.debug(
                f"Entity deleted successfully",
                extra={
                    "entity_type": self.model_class.__name__,
                    "entity_id": entity_id
                }
            )

        except SQLAlchemyError as e:
            self.db.rollback()
            raise RepositoryError(f"Database error during deletion: {str(e)}")

    @handle_repository_errors("soft_delete")
    def soft_delete(self, entity_id: int, deleted_by: Optional[int] = None) -> None:
        """Soft delete entity by ID.

        Args:
            entity_id: Entity identifier
            deleted_by: User ID who performed deletion

        Raises:
            NotFoundError: If entity doesn't exist
            RepositoryError: If deletion fails
        """
        try:
            entity = self.get_by_id(entity_id)
            if not entity:
                raise NotFoundError(f"Entity with ID {entity_id} not found")

            # Set soft delete fields
            if hasattr(entity, 'deleted_at'):
                entity.deleted_at = func.now()
            if hasattr(entity, 'deleted_by') and deleted_by:
                entity.deleted_by = deleted_by

            self.db.commit()

            self.logger.debug(
                f"Entity soft deleted successfully",
                extra={
                    "entity_type": self.model_class.__name__,
                    "entity_id": entity_id,
                    "deleted_by": deleted_by
                }
            )

        except SQLAlchemyError as e:
            self.db.rollback()
            raise RepositoryError(f"Database error during soft deletion: {str(e)}")

    @handle_repository_errors("count")
    def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count entities with optional filters.

        Args:
            filters: Optional filter criteria

        Returns:
            Number of entities matching criteria
        """
        try:
            query = self.db.query(func.count(self.model_class.id)).filter(
                self.model_class.deleted_at.is_(None)
            )

            if filters:
                query = self._apply_filters(query, filters)

            return query.scalar()

        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error during count: {str(e)}")

    @handle_repository_errors("exists")
    def exists(self, entity_id: int) -> bool:
        """Check if entity exists by ID.

        Args:
            entity_id: Entity identifier

        Returns:
            True if entity exists, False otherwise
        """
        try:
            exists = self.db.query(
                self.db.query(self.model_class).filter(
                    self.model_class.id == entity_id,
                    self.model_class.deleted_at.is_(None)
                ).exists()
            ).scalar()

            return exists

        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error during existence check: {str(e)}")

    def _apply_filters(self, query, filters: Dict[str, Any]):
        """Apply filters to query (override in subclasses for custom filtering).

        Args:
            query: SQLAlchemy query object
            filters: Filter criteria

        Returns:
            Filtered query object
        """
        for key, value in filters.items():
            if hasattr(self.model_class, key):
                if isinstance(value, list):
                    query = query.filter(getattr(self.model_class, key).in_(value))
                else:
                    query = query.filter(getattr(self.model_class, key) == value)

        return query
```

#### Concrete Repository Implementation
```python
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, ilike
from core.models.project import Project
from core.repositories.base_repository import BaseRepository

class ProjectRepository(BaseRepository[Project]):
    """Repository for electrical design project data access."""

    def __init__(self, db: Session):
        """Initialize project repository.

        Args:
            db: SQLAlchemy database session
        """
        super().__init__(db, Project)

    @handle_repository_errors("get_by_name")
    def get_by_name(self, name: str) -> Optional[Project]:
        """Get project by name.

        Args:
            name: Project name

        Returns:
            Project instance or None if not found
        """
        try:
            project = self.db.query(Project).filter(
                Project.name == name,
                Project.deleted_at.is_(None)
            ).first()

            return project

        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error during name lookup: {str(e)}")

    @handle_repository_errors("list_with_filters")
    def list_with_filters(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        status: Optional[str] = None,
        created_by: Optional[int] = None,
        voltage_min: Optional[float] = None,
        voltage_max: Optional[float] = None,
        environment_type: Optional[str] = None
    ) -> List[Project]:
        """List projects with advanced filtering.

        Args:
            skip: Number of records to skip
            limit: Maximum records to return
            search: Search term for name/description
            status: Filter by project status
            created_by: Filter by creator user ID
            voltage_min: Minimum voltage level
            voltage_max: Maximum voltage level
            environment_type: Filter by environment type

        Returns:
            List of projects matching criteria
        """
        try:
            query = self.db.query(Project).filter(
                Project.deleted_at.is_(None)
            )

            # Apply search filter
            if search:
                search_term = f"%{search}%"
                query = query.filter(
                    or_(
                        Project.name.ilike(search_term),
                        Project.description.ilike(search_term)
                    )
                )

            # Apply status filter
            if status:
                query = query.filter(Project.status == status)

            # Apply creator filter
            if created_by:
                query = query.filter(Project.created_by == created_by)

            # Apply voltage range filters
            if voltage_min is not None:
                query = query.filter(Project.voltage_level >= voltage_min)
            if voltage_max is not None:
                query = query.filter(Project.voltage_level <= voltage_max)

            # Apply environment type filter
            if environment_type:
                query = query.filter(Project.environment_type == environment_type)

            # Order by creation date (newest first)
            query = query.order_by(desc(Project.created_at))

            # Apply pagination
            projects = query.offset(skip).limit(limit).all()

            return projects

        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error during filtered list: {str(e)}")

    @handle_repository_errors("get_with_relationships")
    def get_with_relationships(self, project_id: int) -> Optional[Project]:
        """Get project with all related entities loaded.

        Args:
            project_id: Project identifier

        Returns:
            Project with relationships loaded or None if not found
        """
        try:
            project = self.db.query(Project).options(
                joinedload(Project.pipes).joinedload('ht_circuit'),
                joinedload(Project.tanks).joinedload('ht_circuit'),
                joinedload(Project.electrical_nodes),
                joinedload(Project.cable_routes),
                joinedload(Project.created_by_user),
                joinedload(Project.updated_by_user)
            ).filter(
                Project.id == project_id,
                Project.deleted_at.is_(None)
            ).first()

            return project

        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error during relationship loading: {str(e)}")

    @handle_repository_errors("get_projects_by_status")
    def get_projects_by_status(self, status: str) -> List[Project]:
        """Get all projects with specific status.

        Args:
            status: Project status to filter by

        Returns:
            List of projects with specified status
        """
        try:
            projects = self.db.query(Project).filter(
                Project.status == status,
                Project.deleted_at.is_(None)
            ).order_by(desc(Project.created_at)).all()

            return projects

        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error during status filter: {str(e)}")

    @handle_repository_errors("get_activity_statistics")
    def get_activity_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get project activity statistics for specified period.

        Args:
            days: Number of days to analyze

        Returns:
            Dictionary containing activity statistics
        """
        try:
            from datetime import datetime, timedelta

            cutoff_date = datetime.utcnow() - timedelta(days=days)

            # Count projects created in period
            projects_created = self.db.query(func.count(Project.id)).filter(
                Project.created_at >= cutoff_date,
                Project.deleted_at.is_(None)
            ).scalar()

            # Count projects updated in period
            projects_updated = self.db.query(func.count(Project.id)).filter(
                Project.updated_at >= cutoff_date,
                Project.created_at < cutoff_date,
                Project.deleted_at.is_(None)
            ).scalar()

            # Count active projects
            active_projects = self.db.query(func.count(Project.id)).filter(
                Project.status == 'active',
                Project.deleted_at.is_(None)
            ).scalar()

            # Count total projects
            total_projects = self.db.query(func.count(Project.id)).filter(
                Project.deleted_at.is_(None)
            ).scalar()

            return {
                "period_days": days,
                "projects_created": projects_created,
                "projects_updated": projects_updated,
                "active_projects": active_projects,
                "total_projects": total_projects,
                "activity_rate": (projects_created + projects_updated) / max(total_projects, 1)
            }

        except SQLAlchemyError as e:
            raise RepositoryError(f"Database error during statistics calculation: {str(e)}")
```

## Schema Layer Design

### Pydantic Schema Architecture

#### Base Schema Patterns
```python
from pydantic import BaseModel, Field, validator, root_validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class BaseSchema(BaseModel):
    """Base schema with common configuration and validation."""

    class Config:
        """Pydantic configuration for optimal performance and validation."""
        # Performance optimizations
        validate_assignment = True
        use_enum_values = True
        str_strip_whitespace = True
        anystr_lower = False

        # JSON handling
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

        # ORM integration
        from_attributes = True

        # Validation settings
        validate_default = True
        extra = "forbid"  # Reject extra fields

        # Schema generation
        schema_extra = {
            "example": {}  # Override in subclasses
        }

class TimestampMixin(BaseModel):
    """Mixin for timestamp fields."""

    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True

class AuditMixin(BaseModel):
    """Mixin for audit trail fields."""

    created_by: int = Field(..., description="Creator user ID")
    updated_by: Optional[int] = Field(None, description="Last updater user ID")

    class Config:
        from_attributes = True

class SoftDeleteMixin(BaseModel):
    """Mixin for soft delete fields."""

    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    deleted_by: Optional[int] = Field(None, description="Deleter user ID")

    class Config:
        from_attributes = True
```

#### Input Validation Schemas
```python
from pydantic import BaseModel, Field, validator
from typing import Optional
from enum import Enum

class EnvironmentType(str, Enum):
    """Environmental classification for electrical installations."""
    INDOOR = "indoor"
    OUTDOOR = "outdoor"
    HAZARDOUS = "hazardous"

class ProjectStatus(str, Enum):
    """Project status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class ProjectCreateSchema(BaseSchema):
    """Schema for creating new electrical design projects."""

    name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Project name (must be unique)",
        example="Industrial Heat Tracing Project"
    )

    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Project description",
        example="Heat tracing system for chemical processing plant"
    )

    voltage_level: float = Field(
        ...,
        gt=0,
        le=1000000,
        description="System voltage level in volts",
        example=480
    )

    environment_type: EnvironmentType = Field(
        ...,
        description="Environmental classification",
        example=EnvironmentType.HAZARDOUS
    )

    ambient_temperature_min: float = Field(
        ...,
        ge=-50,
        le=100,
        description="Minimum ambient temperature in Celsius",
        example=-20
    )

    ambient_temperature_max: float = Field(
        ...,
        ge=-50,
        le=100,
        description="Maximum ambient temperature in Celsius",
        example=40
    )

    client_name: Optional[str] = Field(
        None,
        max_length=255,
        description="Client organization name",
        example="Chemical Processing Corp"
    )

    project_location: Optional[str] = Field(
        None,
        max_length=500,
        description="Project location/address",
        example="Houston, TX, USA"
    )

    design_standards: List[str] = Field(
        default_factory=list,
        description="Applicable design standards",
        example=["IEC-60079", "IEEE-519", "EN-50110"]
    )

    @validator('ambient_temperature_max')
    def validate_temperature_range(cls, v, values):
        """Ensure maximum temperature is greater than minimum."""
        if 'ambient_temperature_min' in values:
            min_temp = values['ambient_temperature_min']
            if v <= min_temp:
                raise ValueError('Maximum temperature must be greater than minimum temperature')
            if v - min_temp > 150:
                raise ValueError('Temperature range exceeds reasonable limits (150°C)')
        return v

    @validator('voltage_level')
    def validate_voltage_level(cls, v):
        """Validate voltage level against engineering standards."""
        # Standard voltage levels per IEC 60038
        standard_voltages = [
            120, 208, 240, 277, 480, 600,  # Low voltage
            2400, 4160, 6900, 13800,       # Medium voltage
            23000, 34500, 46000, 69000,    # High voltage
            115000, 138000, 230000, 345000, 500000, 765000  # Extra high voltage
        ]

        # Check if voltage is within 5% of standard voltage
        is_standard = any(abs(v - std_v) / std_v <= 0.05 for std_v in standard_voltages)

        if not is_standard:
            # Log warning but allow non-standard voltages
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Non-standard voltage level specified: {v}V")

        return v

    @validator('design_standards')
    def validate_design_standards(cls, v):
        """Validate design standards are approved."""
        approved_standards = [
            # IEEE Standards
            "IEEE-80", "IEEE-142", "IEEE-399", "IEEE-519",
            # IEC Standards
            "IEC-60079", "IEC-61508", "IEC-60364", "IEC-60287",
            # EN Standards
            "EN-50110", "EN-60204", "EN-50522"
        ]

        for standard in v:
            if standard not in approved_standards:
                raise ValueError(f"Unapproved design standard: {standard}")

        return v

    @root_validator
    def validate_hazardous_environment(cls, values):
        """Additional validation for hazardous environments."""
        if values.get('environment_type') == EnvironmentType.HAZARDOUS:
            design_standards = values.get('design_standards', [])
            if 'IEC-60079' not in design_standards:
                raise ValueError('Hazardous environments must include IEC-60079 standard')

        return values

class ProjectUpdateSchema(BaseSchema):
    """Schema for updating existing projects."""

    name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=255,
        description="Project name (must be unique)"
    )

    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Project description"
    )

    voltage_level: Optional[float] = Field(
        None,
        gt=0,
        le=1000000,
        description="System voltage level in volts"
    )

    environment_type: Optional[EnvironmentType] = Field(
        None,
        description="Environmental classification"
    )

    ambient_temperature_min: Optional[float] = Field(
        None,
        ge=-50,
        le=100,
        description="Minimum ambient temperature in Celsius"
    )

    ambient_temperature_max: Optional[float] = Field(
        None,
        ge=-50,
        le=100,
        description="Maximum ambient temperature in Celsius"
    )

    status: Optional[ProjectStatus] = Field(
        None,
        description="Project status"
    )

    client_name: Optional[str] = Field(
        None,
        max_length=255,
        description="Client organization name"
    )

    project_location: Optional[str] = Field(
        None,
        max_length=500,
        description="Project location/address"
    )

    design_standards: Optional[List[str]] = Field(
        None,
        description="Applicable design standards"
    )

    # Apply same validators as create schema
    _validate_temperature_range = validator('ambient_temperature_max', allow_reuse=True)(
        ProjectCreateSchema.validate_temperature_range
    )
    _validate_voltage_level = validator('voltage_level', allow_reuse=True)(
        ProjectCreateSchema.validate_voltage_level
    )
    _validate_design_standards = validator('design_standards', allow_reuse=True)(
        ProjectCreateSchema.validate_design_standards
    )
```

#### Output Response Schemas
```python
from pydantic import BaseModel, Field, computed_field
from typing import Optional, List, Dict, Any
from datetime import datetime

class ProjectResponse(BaseSchema, TimestampMixin, AuditMixin):
    """Response schema for project data."""

    id: int = Field(..., description="Unique project identifier")
    name: str = Field(..., description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    voltage_level: float = Field(..., description="System voltage level in volts")
    environment_type: EnvironmentType = Field(..., description="Environmental classification")
    ambient_temperature_min: float = Field(..., description="Minimum ambient temperature")
    ambient_temperature_max: float = Field(..., description="Maximum ambient temperature")
    status: ProjectStatus = Field(..., description="Project status")
    client_name: Optional[str] = Field(None, description="Client organization name")
    project_location: Optional[str] = Field(None, description="Project location")
    design_standards: List[str] = Field(default_factory=list, description="Design standards")

    # Computed fields
    @computed_field
    @property
    def temperature_range(self) -> float:
        """Calculate temperature range."""
        return self.ambient_temperature_max - self.ambient_temperature_min

    @computed_field
    @property
    def voltage_category(self) -> str:
        """Classify voltage level according to IEC standards."""
        if self.voltage_level <= 1000:
            return "low_voltage"
        elif self.voltage_level <= 35000:
            return "medium_voltage"
        elif self.voltage_level <= 100000:
            return "high_voltage"
        else:
            return "extra_high_voltage"

    @computed_field
    @property
    def safety_classification(self) -> str:
        """Determine safety classification based on environment and voltage."""
        if self.environment_type == EnvironmentType.HAZARDOUS:
            return "atex_required"
        elif self.voltage_level > 1000:
            return "high_voltage_safety"
        elif self.environment_type == EnvironmentType.OUTDOOR:
            return "weather_protection"
        else:
            return "standard_safety"

class ProjectListResponse(BaseModel):
    """Response schema for project list with pagination."""

    projects: List[ProjectResponse] = Field(..., description="List of projects")
    total_count: int = Field(..., description="Total number of projects")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_previous: bool = Field(..., description="Whether there are previous pages")

class ProjectStatisticsResponse(BaseModel):
    """Response schema for project statistics."""

    project_id: int = Field(..., description="Project identifier")
    total_pipes: int = Field(..., description="Total number of pipes")
    total_tanks: int = Field(..., description="Total number of tanks")
    total_heat_circuits: int = Field(..., description="Total heat tracing circuits")
    total_pipe_length: Optional[float] = Field(None, description="Total pipe length in meters")
    average_pipe_diameter: Optional[float] = Field(None, description="Average pipe diameter in meters")
    voltage_level: float = Field(..., description="System voltage level")
    temperature_range: float = Field(..., description="Temperature range")
    environment_type: EnvironmentType = Field(..., description="Environment type")
    safety_classification: str = Field(..., description="Safety classification")
    created_at: datetime = Field(..., description="Project creation date")
    last_modified: datetime = Field(..., description="Last modification date")
```

## Model Layer Architecture

### SQLAlchemy ORM Models

#### Base Model with Common Patterns
```python
from sqlalchemy import Column, Integer, DateTime, String, Boolean, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Mapped, mapped_column
from typing import Optional
from datetime import datetime

Base = declarative_base()

class BaseModel(Base):
    """Base model with common fields and functionality."""

    __abstract__ = True

    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)

    # Timestamp fields
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="Creation timestamp"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="Last update timestamp"
    )

    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"

class AuditMixin:
    """Mixin for audit trail fields."""

    created_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="User ID who created this record"
    )

    updated_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="User ID who last updated this record"
    )

class SoftDeleteMixin:
    """Mixin for soft delete functionality."""

    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Soft deletion timestamp"
    )

    deleted_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="User ID who deleted this record"
    )

    @property
    def is_deleted(self) -> bool:
        """Check if record is soft deleted."""
        return self.deleted_at is not None
```

#### Project Model Implementation
```python
from sqlalchemy import Column, Integer, String, Float, Enum, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import ARRAY
from typing import List, Optional
import enum

class EnvironmentType(enum.Enum):
    """Environmental classification enumeration."""
    INDOOR = "indoor"
    OUTDOOR = "outdoor"
    HAZARDOUS = "hazardous"

class ProjectStatus(enum.Enum):
    """Project status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class Project(BaseModel, AuditMixin, SoftDeleteMixin):
    """Electrical design project model.

    Represents a complete electrical design project with environmental
    parameters, design specifications, and relationships to all project
    components including pipes, tanks, electrical nodes, and circuits.

    Attributes:
        name: Unique project name
        description: Optional project description
        voltage_level: System voltage level in volts
        environment_type: Environmental classification (indoor/outdoor/hazardous)
        ambient_temperature_min: Minimum ambient temperature in Celsius
        ambient_temperature_max: Maximum ambient temperature in Celsius
        status: Current project status
        client_name: Client organization name
        project_location: Project location/address
        design_standards: List of applicable design standards

    Relationships:
        pipes: Heat tracing pipes in this project
        tanks: Heat tracing tanks in this project
        electrical_nodes: Electrical connection points
        cable_routes: Cable routing definitions
        switchboards: Electrical distribution panels
    """

    __tablename__ = "projects"

    # Core project information
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Project name (must be unique)"
    )

    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Project description"
    )

    # Electrical specifications
    voltage_level: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        doc="System voltage level in volts"
    )

    # Environmental parameters
    environment_type: Mapped[EnvironmentType] = mapped_column(
        Enum(EnvironmentType),
        nullable=False,
        doc="Environmental classification"
    )

    ambient_temperature_min: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        doc="Minimum ambient temperature in Celsius"
    )

    ambient_temperature_max: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        doc="Maximum ambient temperature in Celsius"
    )

    # Project management
    status: Mapped[ProjectStatus] = mapped_column(
        Enum(ProjectStatus),
        nullable=False,
        default=ProjectStatus.DRAFT,
        doc="Current project status"
    )

    # Client information
    client_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Client organization name"
    )

    project_location: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        doc="Project location/address"
    )

    # Design standards (stored as JSON array)
    design_standards: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        nullable=True,
        default=list,
        doc="Applicable design standards (IEEE/IEC/EN)"
    )

    # Relationships
    pipes: Mapped[List["Pipe"]] = relationship(
        "Pipe",
        back_populates="project",
        cascade="all, delete-orphan",
        lazy="select",
        doc="Heat tracing pipes in this project"
    )

    tanks: Mapped[List["Tank"]] = relationship(
        "Tank",
        back_populates="project",
        cascade="all, delete-orphan",
        lazy="select",
        doc="Heat tracing tanks in this project"
    )

    electrical_nodes: Mapped[List["ElectricalNode"]] = relationship(
        "ElectricalNode",
        back_populates="project",
        cascade="all, delete-orphan",
        lazy="select",
        doc="Electrical connection points"
    )

    cable_routes: Mapped[List["CableRoute"]] = relationship(
        "CableRoute",
        back_populates="project",
        cascade="all, delete-orphan",
        lazy="select",
        doc="Cable routing definitions"
    )

    switchboards: Mapped[List["Switchboard"]] = relationship(
        "Switchboard",
        back_populates="project",
        cascade="all, delete-orphan",
        lazy="select",
        doc="Electrical distribution panels"
    )

    # Audit relationships
    created_by_user: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[AuditMixin.created_by],
        lazy="select",
        doc="User who created this project"
    )

    updated_by_user: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[AuditMixin.updated_by],
        lazy="select",
        doc="User who last updated this project"
    )

    # Table constraints
    __table_args__ = (
        UniqueConstraint('name', name='uq_project_name'),
        # Check constraints for temperature range
        CheckConstraint(
            'ambient_temperature_max > ambient_temperature_min',
            name='ck_project_temperature_range'
        ),
        # Check constraint for voltage level
        CheckConstraint(
            'voltage_level > 0',
            name='ck_project_voltage_positive'
        ),
    )

    @property
    def temperature_range(self) -> float:
        """Calculate temperature range in Celsius."""
        return self.ambient_temperature_max - self.ambient_temperature_min

    @property
    def voltage_category(self) -> str:
        """Classify voltage level according to IEC standards."""
        if self.voltage_level <= 1000:
            return "low_voltage"
        elif self.voltage_level <= 35000:
            return "medium_voltage"
        elif self.voltage_level <= 100000:
            return "high_voltage"
        else:
            return "extra_high_voltage"

    @property
    def total_pipe_length(self) -> float:
        """Calculate total length of all pipes in project."""
        return sum(pipe.length for pipe in self.pipes if pipe.length)

    @property
    def total_heat_circuits(self) -> int:
        """Count total number of heat tracing circuits."""
        return sum(1 for pipe in self.pipes if pipe.ht_circuit) + \
               sum(1 for tank in self.tanks if tank.ht_circuit)

    def __repr__(self) -> str:
        """String representation of the project."""
        return f"<Project(id={self.id}, name='{self.name}', status='{self.status.value}')>"
```

---

**Navigation:**
← [Previous: Unified Patterns](040-unified-patterns.md) | [Handbook Home](001-cover.md) | [Next: Frontend Transition](060-frontend-transition.md) →

**Related Documentation:**
- [Design Principles](../002-robust-design-principles.md)
- [Backend Architecture Specifications](./backend/000-backend-specification.md)
- [Repository Pattern Implementation](../../backend/core/repositories/)
- [Service Layer Documentation](../../backend/core/services/)
- [Schema Validation Patterns](../../backend/core/schemas/)